{"version": 3, "file": "static/css/main.dd9e21c1.css", "mappings": "kGAAA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,sCAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAAd,2CAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,wBAAc,CAAd,wDAAc,CAAd,aAAc,CAAd,4CAAc,CAsBV,8CAAgF,CAAhF,sDAAgF,CAAhF,kBAAgF,CAAhF,kBAAgF,CAAhF,qBAAgF,CAAhF,gBAAgF,CAAhF,+CAAgF,CAAhF,kGAAgF,CAAhF,mBAAgF,CAAhF,iBAAgF,CAAhF,eAAgF,CAAhF,sBAAgF,CAAhF,mBAAgF,CAAhF,kBAAgF,CAAhF,iHAAgF,CAAhF,kDAAgF,CAAhF,6HAAgF,CAAhF,wGAAgF,CAAhF,kGAAgF,CAAhF,wFAAgF,CAAhF,uBAAgF,CAAhF,kBAAgF,CAAhF,8BAAgF,CAAhF,mBAAgF,CAAhF,wBAAgF,CAAhF,sDAAgF,CAAhF,UAAgF,CAAhF,+CAAgF,CAAhF,oCAAgF,CAAhF,wBAAgF,CAAhF,sDAAgF,CAAhF,sCAAgF,CAAhF,wDAAgF,CAQhF,8CAAyF,CAAzF,sDAAyF,CAAzF,kBAAyF,CAAzF,kBAAyF,CAAzF,qBAAyF,CAAzF,gBAAyF,CAAzF,+CAAyF,CAAzF,kGAAyF,CAAzF,mBAAyF,CAAzF,iBAAyF,CAAzF,eAAyF,CAAzF,sBAAyF,CAAzF,mBAAyF,CAAzF,kBAAyF,CAAzF,iHAAyF,CAAzF,kDAAyF,CAAzF,6HAAyF,CAAzF,wGAAyF,CAAzF,kGAAyF,CAAzF,wFAAyF,CAAzF,uBAAyF,CAAzF,kBAAyF,CAAzF,kCAAyF,CAAzF,iBAAyF,CAAzF,mBAAyF,CAAzF,qBAAyF,CAAzF,wDAAyF,CAAzF,oBAAyF,CAAzF,wDAAyF,CAAzF,aAAyF,CAAzF,4CAAyF,CAAzF,oCAAyF,CAAzF,wBAAyF,CAAzF,wDAAyF,CAAzF,sCAAyF,CAAzF,wDAAyF,CAIzF,2BAA6D,CAA7D,iBAA6D,CAA7D,gEAA6D,CAA7D,kGAA6D,CAA7D,qBAA6D,CAA7D,wDAA6D,CAA7D,oBAA6D,CAA7D,wDAA6D,CAA7D,mBAA6D,CAA7D,gBAA6D,CAA7D,+CAA6D,CAA7D,kGAA6D,CAI7D,4BAA4K,CAA5K,oBAA4K,CAA5K,wDAA4K,CAA5K,qBAA4K,CAA5K,gBAA4K,CAA5K,aAA4K,CAA5K,+BAA4K,CAA5K,8CAA4K,CAA5K,aAA4K,CAA5K,sDAA4K,CAA5K,wCAA4K,CAA5K,sDAA4K,CAA5K,+CAA4K,CAA5K,kGAA4K,CAA5K,kCAA4K,CAA5K,mBAA4K,CAA5K,6EAA4K,CAA5K,uDAA4K,CAA5K,uBAA4K,CAA5K,kBAA4K,CAA5K,iDAA4K,CAA5K,mBAA4K,EAI5K,6BAAiC,CAAjC,oBAAiC,CAAjC,wDAAiC,CAAjC,qBAAiC,CAAjC,gBAAiC,CAAjC,aAAiC,CAAjC,+BAAiC,CAAjC,+CAAiC,CAAjC,aAAiC,CAAjC,sDAAiC,CAAjC,yCAAiC,CAAjC,sDAAiC,CAAjC,+CAAiC,CAAjC,kGAAiC,CAAjC,mCAAiC,CAAjC,mBAAiC,CAAjC,6EAAiC,CAAjC,uDAAiC,CAAjC,uBAAiC,CAAjC,kBAAiC,CAAjC,kDAAiC,CAAjC,mBAAiC,EAAjC,sBAAiC,CAAjC,oBAAiC,CAIjC,yBAA8E,CAA9E,oBAA8E,CAA9E,mBAA8E,CAA9E,gBAA8E,CAA9E,gCAA8E,CAA9E,uBAA8E,CAI9E,uCAA4C,CAA5C,wDAA4C,CAA5C,aAA4C,CAA5C,6CAA4C,CAI5C,8BAJA,iBAA4C,CAA5C,mBAA4C,CAA5C,kBAA4C,CAA5C,oBAA4C,CAA5C,mBAA4C,CAA5C,gBAA4C,CAA5C,eAA4C,CAA5C,gBAA4C,CAA5C,uBAI4C,CAA5C,uCAA4C,CAA5C,wDAA4C,CAA5C,aAA4C,CAA5C,6CAA4C,CAI5C,sCAA0C,CAA1C,wDAA0C,CAA1C,aAA0C,CAA1C,6CAA0C,CAI1C,0BAJA,iBAA0C,CAA1C,mBAA0C,CAA1C,kBAA0C,CAA1C,oBAA0C,CAA1C,mBAA0C,CAA1C,gBAA0C,CAA1C,eAA0C,CAA1C,gBAA0C,CAA1C,uBAI4C,CAA5C,oCAA4C,CAA5C,wDAA4C,CAA5C,aAA4C,CAA5C,6CAA4C,CAI5C,qBAA0C,CAA1C,4DAA0C,CAA1C,qBAA0C,CAA1C,wDAA0C,CAA1C,oBAA0C,CAA1C,oHAA0C,CAI1C,+BAAiB,CAAjB,wBAAiB,CAAjB,wDAAiB,CAIjB,sCAAqJ,CAArJ,aAAqJ,CAArJ,+CAAqJ,CAArJ,cAAqJ,CAArJ,gBAAqJ,CAArJ,eAAqJ,CAArJ,oBAAqJ,CAArJ,gBAAqJ,CAArJ,qBAAqJ,CAArJ,eAAqJ,CAArJ,wBAAqJ,CAArJ,kHAAqJ,CAArJ,kDAAqJ,CAArJ,0CAAqJ,CAArJ,wBAAqJ,CAArJ,wDAAqJ,CAIrJ,4BAA+D,CAA/D,qBAA+D,CAA/D,wDAA+D,CAA/D,kHAA+D,CAA/D,kDAA+D,CAA/D,kCAA+D,CAA/D,wBAA+D,CAA/D,wDAA+D,CAI/D,+BAAwD,CAAxD,aAAwD,CAAxD,8DAAwD,CAAxD,mBAAwD,CAAxD,mBAAwD,CAAxD,kBAAwD,CAIxD,sCAAsE,CAAtE,iCAAsE,CAAtE,uBAAsE,CAAtE,oBAAsE,CAAtE,2EAAsE,CAAtE,WAAsE,CAAtE,UAAsE,CApF1E,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,aAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,qBAAmB,CAAnB,8BAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,wCAAmB,CAAnB,2NAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iEAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,oEAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,+CAAmB,CAAnB,+BAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,8EAAmB,CAAnB,gFAAmB,CAAnB,wBAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,mCAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,kEAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,uEAAmB,CAAnB,wFAAmB,CAAnB,+BAAmB,CAAnB,yDAAmB,CAAnB,wLAAmB,CAFnB,kDAwGA,CAxGA,2CAwGA,CAxGA,wBAwGA,CAxGA,wDAwGA,CAxGA,0CAwGA,CAxGA,wBAwGA,CAxGA,wDAwGA,CAxGA,8CAwGA,CAxGA,wBAwGA,CAxGA,sDAwGA,CAxGA,+CAwGA,CAxGA,aAwGA,CAxGA,4CAwGA,CAxGA,+CAwGA,CAxGA,aAwGA,CAxGA,4CAwGA,CAxGA,+CAwGA,CAxGA,aAwGA,CAxGA,4CAwGA,CAxGA,kDAwGA,CAxGA,aAwGA,CAxGA,8CAwGA,CAxGA,kDAwGA,CAxGA,aAwGA,CAxGA,6CAwGA,CAxGA,kDAwGA,CAxGA,kBAwGA,CAxGA,+HAwGA,CAxGA,wGAwGA,CAxGA,uEAwGA,CAxGA,wFAwGA,CAxGA,8CAwGA,CAxGA,kDAwGA,CAxGA,wDAwGA,CAxGA,4CAwGA,CAxGA,yDAwGA,CAxGA,sDAwGA,CAxGA,yDAwGA,CAxGA,yCAwGA,CAxGA,kEAwGA,CAxGA,6BAwGA,CAxGA,iBAwGA,CAxGA,sBAwGA,CAxGA,wBAwGA,CAxGA,sBAwGA,CAxGA,6BAwGA,CAxGA,oBAwGA,CAxGA,8DAwGA,CAxGA,8DAwGA,CAxGA,oCAwGA,CAxGA,kDAwGA,CAxGA,6BAwGA,CAxGA,sBAwGA,CAxGA,kBAwGA,CAxGA,mCAwGA,CAxGA,8BAwGA,CAxGA,oBAwGA,CAxGA,6BAwGA,CAxGA,oBAwGA,CAxGA,gCAwGA,CAxGA,mBAwGA,EAxGA,mDAwGA,CAxGA,sBAwGA,CAxGA,sBAwGA,CAxGA,oCAwGA,CAxGA,kDAwGA,EAxGA,mDAwGA,CAxGA,6BAwGA,CAxGA,yCAwGA,CAxGA,yCAwGA,CAxGA,sBAwGA,CAxGA,wBAwGA,CAxGA,qBAwGA,CAxGA,8DAwGA,CAxGA,8DAwGA,CAxGA,8DAwGA,CAxGA,8DAwGA,CAxGA,mCAwGA,CAxGA,2BAwGA,CAxGA,kBAwGA,CAxGA,6BAwGA", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n\n@layer base {\n  html {\n    font-family: 'Inter', system-ui, sans-serif;\n  }\n  \n  body {\n    @apply bg-gray-50 text-gray-900;\n  }\n}\n\n@layer components {\n  .btn {\n    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;\n  }\n  \n  .btn-primary {\n    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;\n  }\n  \n  .btn-secondary {\n    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;\n  }\n  \n  .btn-outline {\n    @apply btn bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-primary-500;\n  }\n  \n  .card {\n    @apply bg-white rounded-lg shadow-soft border border-gray-200;\n  }\n  \n  .input {\n    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;\n  }\n  \n  .select {\n    @apply input pr-10 cursor-pointer;\n  }\n  \n  .badge {\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\n  }\n  \n  .badge-success {\n    @apply badge bg-success-100 text-success-800;\n  }\n  \n  .badge-warning {\n    @apply badge bg-warning-100 text-warning-800;\n  }\n  \n  .badge-danger {\n    @apply badge bg-danger-100 text-danger-800;\n  }\n  \n  .badge-info {\n    @apply badge bg-primary-100 text-primary-800;\n  }\n  \n  .table {\n    @apply min-w-full divide-y divide-gray-200;\n  }\n  \n  .table-header {\n    @apply bg-gray-50;\n  }\n  \n  .table-header-cell {\n    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-150;\n  }\n  \n  .table-row {\n    @apply bg-white hover:bg-gray-50 transition-colors duration-150;\n  }\n  \n  .table-cell {\n    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;\n  }\n  \n  .loading-spinner {\n    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;\n  }\n}\n\n@layer utilities {\n  .text-truncate {\n    @apply truncate;\n  }\n  \n  .scrollbar-hide {\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n  }\n  \n  .scrollbar-hide::-webkit-scrollbar {\n    display: none;\n  }\n}\n"], "names": [], "sourceRoot": ""}