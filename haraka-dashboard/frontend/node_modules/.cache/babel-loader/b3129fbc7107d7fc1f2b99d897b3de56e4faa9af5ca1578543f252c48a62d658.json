{"ast": null, "code": "import{useState,useEffect}from'react';import axios from'axios';import{useAuth}from'../contexts/AuthContext';// Create axios instance with default config\nconst api=axios.create({baseURL:process.env.REACT_APP_API_URL||'/api',timeout:10000});// Request interceptor\napi.interceptors.request.use(config=>{// Add any auth headers here if needed\nreturn config;},error=>{return Promise.reject(error);});// Response interceptor\napi.interceptors.response.use(response=>{return response;},error=>{var _error$response;console.error('API Error:',((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data)||error.message);return Promise.reject(error);});// Custom hook for API calls\nexport const useApi=function(url){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const[data,setData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const{isAuthenticated,loading:authLoading}=useAuth();const fetchData=async()=>{try{setLoading(true);setError(null);const response=await api.get(url,options);setData(response.data);}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||err.message||'An error occurred');}finally{setLoading(false);}};useEffect(()=>{// Only fetch data if user is authenticated and auth is not loading\nif(!authLoading&&isAuthenticated){fetchData();}else if(!authLoading&&!isAuthenticated){// Clear data if not authenticated\nsetData(null);setLoading(false);setError(null);}},[url,isAuthenticated,authLoading]);const refetch=()=>{fetchData();};return{data,loading,error,refetch};};// Hook for transactions with pagination and filtering\nexport const useTransactions=function(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const[data,setData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const{isAuthenticated,loading:authLoading}=useAuth();const fetchTransactions=async function(){let newParams=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};try{setLoading(true);setError(null);const queryParams={...params,...newParams};const queryString=new URLSearchParams(queryParams).toString();const url=`/transactions${queryString?`?${queryString}`:''}`;const response=await api.get(url);setData(response.data);}catch(err){var _err$response2,_err$response2$data;setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||err.message||'Failed to fetch transactions');}finally{setLoading(false);}};useEffect(()=>{// Only fetch data if user is authenticated and auth is not loading\nif(!authLoading&&isAuthenticated){fetchTransactions();}else if(!authLoading&&!isAuthenticated){// Clear data if not authenticated\nsetData(null);setLoading(false);setError(null);}},[isAuthenticated,authLoading]);const refetch=function(){let newParams=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};fetchTransactions(newParams);};return{data,loading,error,refetch};};// Hook for statistics\nexport const useStats=function(){let timeframe=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'24h';const[data,setData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const{isAuthenticated,loading:authLoading}=useAuth();const fetchStats=async function(){let newTimeframe=arguments.length>0&&arguments[0]!==undefined?arguments[0]:timeframe;try{setLoading(true);setError(null);const response=await api.get(`/stats?timeframe=${newTimeframe}`);setData(response.data);}catch(err){var _err$response3,_err$response3$data;setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||err.message||'Failed to fetch statistics');}finally{setLoading(false);}};useEffect(()=>{// Only fetch data if user is authenticated and auth is not loading\nif(!authLoading&&isAuthenticated){fetchStats();}else if(!authLoading&&!isAuthenticated){// Clear data if not authenticated\nsetData(null);setLoading(false);setError(null);}},[timeframe,isAuthenticated,authLoading]);const refetch=function(){let newTimeframe=arguments.length>0&&arguments[0]!==undefined?arguments[0]:timeframe;fetchStats(newTimeframe);};return{data,loading,error,refetch};};// Hook for single transaction\nexport const useTransaction=id=>{const[data,setData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const{isAuthenticated,loading:authLoading}=useAuth();useEffect(()=>{if(!id)return;const fetchTransaction=async()=>{try{setLoading(true);setError(null);const response=await api.get(`/transactions/${id}`);setData(response.data);}catch(err){var _err$response4,_err$response4$data;setError(((_err$response4=err.response)===null||_err$response4===void 0?void 0:(_err$response4$data=_err$response4.data)===null||_err$response4$data===void 0?void 0:_err$response4$data.message)||err.message||'Failed to fetch transaction');}finally{setLoading(false);}};// Only fetch data if user is authenticated and auth is not loading\nif(!authLoading&&isAuthenticated){fetchTransaction();}else if(!authLoading&&!isAuthenticated){// Clear data if not authenticated\nsetData(null);setLoading(false);setError(null);}},[id,isAuthenticated,authLoading]);return{data,loading,error};};export default api;", "map": {"version": 3, "names": ["useState", "useEffect", "axios", "useAuth", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "_error$response", "console", "data", "message", "useApi", "url", "options", "arguments", "length", "undefined", "setData", "loading", "setLoading", "setError", "isAuthenticated", "authLoading", "fetchData", "get", "err", "_err$response", "_err$response$data", "refetch", "useTransactions", "params", "fetchTransactions", "newParams", "queryParams", "queryString", "URLSearchParams", "toString", "_err$response2", "_err$response2$data", "useStats", "timeframe", "fetchStats", "newTimeframe", "_err$response3", "_err$response3$data", "useTransaction", "id", "fetchTransaction", "_err$response4", "_err$response4$data"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '/api',\n  timeout: 10000,\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    // Add any auth headers here if needed\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    console.error('API Error:', error.response?.data || error.message);\n    return Promise.reject(error);\n  }\n);\n\n// Custom hook for API calls\nexport const useApi = (url, options = {}) => {\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await api.get(url, options);\n      setData(response.data);\n    } catch (err) {\n      setError(err.response?.data?.message || err.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // Only fetch data if user is authenticated and auth is not loading\n    if (!authLoading && isAuthenticated) {\n      fetchData();\n    } else if (!authLoading && !isAuthenticated) {\n      // Clear data if not authenticated\n      setData(null);\n      setLoading(false);\n      setError(null);\n    }\n  }, [url, isAuthenticated, authLoading]);\n\n  const refetch = () => {\n    fetchData();\n  };\n\n  return { data, loading, error, refetch };\n};\n\n// Hook for transactions with pagination and filtering\nexport const useTransactions = (params = {}) => {\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  const fetchTransactions = async (newParams = {}) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const queryParams = { ...params, ...newParams };\n      const queryString = new URLSearchParams(queryParams).toString();\n      const url = `/transactions${queryString ? `?${queryString}` : ''}`;\n      \n      const response = await api.get(url);\n      setData(response.data);\n    } catch (err) {\n      setError(err.response?.data?.message || err.message || 'Failed to fetch transactions');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // Only fetch data if user is authenticated and auth is not loading\n    if (!authLoading && isAuthenticated) {\n      fetchTransactions();\n    } else if (!authLoading && !isAuthenticated) {\n      // Clear data if not authenticated\n      setData(null);\n      setLoading(false);\n      setError(null);\n    }\n  }, [isAuthenticated, authLoading]);\n\n  const refetch = (newParams = {}) => {\n    fetchTransactions(newParams);\n  };\n\n  return { data, loading, error, refetch };\n};\n\n// Hook for statistics\nexport const useStats = (timeframe = '24h') => {\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  const fetchStats = async (newTimeframe = timeframe) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await api.get(`/stats?timeframe=${newTimeframe}`);\n      setData(response.data);\n    } catch (err) {\n      setError(err.response?.data?.message || err.message || 'Failed to fetch statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // Only fetch data if user is authenticated and auth is not loading\n    if (!authLoading && isAuthenticated) {\n      fetchStats();\n    } else if (!authLoading && !isAuthenticated) {\n      // Clear data if not authenticated\n      setData(null);\n      setLoading(false);\n      setError(null);\n    }\n  }, [timeframe, isAuthenticated, authLoading]);\n\n  const refetch = (newTimeframe = timeframe) => {\n    fetchStats(newTimeframe);\n  };\n\n  return { data, loading, error, refetch };\n};\n\n// Hook for single transaction\nexport const useTransaction = (id) => {\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  useEffect(() => {\n    if (!id) return;\n\n    const fetchTransaction = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const response = await api.get(`/transactions/${id}`);\n        setData(response.data);\n      } catch (err) {\n        setError(err.response?.data?.message || err.message || 'Failed to fetch transaction');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Only fetch data if user is authenticated and auth is not loading\n    if (!authLoading && isAuthenticated) {\n      fetchTransaction();\n    } else if (!authLoading && !isAuthenticated) {\n      // Clear data if not authenticated\n      setData(null);\n      setLoading(false);\n      setError(null);\n    }\n  }, [id, isAuthenticated, authLoading]);\n\n  return { data, loading, error };\n};\n\nexport default api;\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,yBAAyB,CAEjD;AACA,KAAM,CAAAC,GAAG,CAAGF,KAAK,CAACG,MAAM,CAAC,CACvBC,OAAO,CAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,MAAM,CAChDC,OAAO,CAAE,KACX,CAAC,CAAC,CAEF;AACAN,GAAG,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV;AACA,MAAO,CAAAA,MAAM,CACf,CAAC,CACAC,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAX,GAAG,CAACO,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC1BK,QAAQ,EAAK,CACZ,MAAO,CAAAA,QAAQ,CACjB,CAAC,CACAH,KAAK,EAAK,KAAAI,eAAA,CACTC,OAAO,CAACL,KAAK,CAAC,YAAY,CAAE,EAAAI,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBE,IAAI,GAAIN,KAAK,CAACO,OAAO,CAAC,CAClE,MAAO,CAAAN,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAQ,MAAM,CAAG,QAAAA,CAACC,GAAG,CAAmB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACtC,KAAM,CAACL,IAAI,CAAEQ,OAAO,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACe,KAAK,CAAEiB,QAAQ,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAEiC,eAAe,CAAEH,OAAO,CAAEI,WAAY,CAAC,CAAG/B,OAAO,CAAC,CAAC,CAE3D,KAAM,CAAAgC,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFJ,UAAU,CAAC,IAAI,CAAC,CAChBC,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAd,GAAG,CAACgC,GAAG,CAACZ,GAAG,CAAEC,OAAO,CAAC,CAC5CI,OAAO,CAACX,QAAQ,CAACG,IAAI,CAAC,CACxB,CAAE,MAAOgB,GAAG,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACZP,QAAQ,CAAC,EAAAM,aAAA,CAAAD,GAAG,CAACnB,QAAQ,UAAAoB,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcjB,IAAI,UAAAkB,kBAAA,iBAAlBA,kBAAA,CAAoBjB,OAAO,GAAIe,GAAG,CAACf,OAAO,EAAI,mBAAmB,CAAC,CAC7E,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED9B,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACiC,WAAW,EAAID,eAAe,CAAE,CACnCE,SAAS,CAAC,CAAC,CACb,CAAC,IAAM,IAAI,CAACD,WAAW,EAAI,CAACD,eAAe,CAAE,CAC3C;AACAJ,OAAO,CAAC,IAAI,CAAC,CACbE,UAAU,CAAC,KAAK,CAAC,CACjBC,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAC,CAAE,CAACR,GAAG,CAAES,eAAe,CAAEC,WAAW,CAAC,CAAC,CAEvC,KAAM,CAAAM,OAAO,CAAGA,CAAA,GAAM,CACpBL,SAAS,CAAC,CAAC,CACb,CAAC,CAED,MAAO,CAAEd,IAAI,CAAES,OAAO,CAAEf,KAAK,CAAEyB,OAAQ,CAAC,CAC1C,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,eAAe,CAAG,QAAAA,CAAA,CAAiB,IAAhB,CAAAC,MAAM,CAAAhB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACzC,KAAM,CAACL,IAAI,CAAEQ,OAAO,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACe,KAAK,CAAEiB,QAAQ,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAEiC,eAAe,CAAEH,OAAO,CAAEI,WAAY,CAAC,CAAG/B,OAAO,CAAC,CAAC,CAE3D,KAAM,CAAAwC,iBAAiB,CAAG,cAAAA,CAAA,CAA0B,IAAnB,CAAAC,SAAS,CAAAlB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC7C,GAAI,CACFK,UAAU,CAAC,IAAI,CAAC,CAChBC,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAa,WAAW,CAAG,CAAE,GAAGH,MAAM,CAAE,GAAGE,SAAU,CAAC,CAC/C,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAC,eAAe,CAACF,WAAW,CAAC,CAACG,QAAQ,CAAC,CAAC,CAC/D,KAAM,CAAAxB,GAAG,CAAG,gBAAgBsB,WAAW,CAAG,IAAIA,WAAW,EAAE,CAAG,EAAE,EAAE,CAElE,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAAd,GAAG,CAACgC,GAAG,CAACZ,GAAG,CAAC,CACnCK,OAAO,CAACX,QAAQ,CAACG,IAAI,CAAC,CACxB,CAAE,MAAOgB,GAAG,CAAE,KAAAY,cAAA,CAAAC,mBAAA,CACZlB,QAAQ,CAAC,EAAAiB,cAAA,CAAAZ,GAAG,CAACnB,QAAQ,UAAA+B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc5B,IAAI,UAAA6B,mBAAA,iBAAlBA,mBAAA,CAAoB5B,OAAO,GAAIe,GAAG,CAACf,OAAO,EAAI,8BAA8B,CAAC,CACxF,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED9B,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACiC,WAAW,EAAID,eAAe,CAAE,CACnCU,iBAAiB,CAAC,CAAC,CACrB,CAAC,IAAM,IAAI,CAACT,WAAW,EAAI,CAACD,eAAe,CAAE,CAC3C;AACAJ,OAAO,CAAC,IAAI,CAAC,CACbE,UAAU,CAAC,KAAK,CAAC,CACjBC,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAC,CAAE,CAACC,eAAe,CAAEC,WAAW,CAAC,CAAC,CAElC,KAAM,CAAAM,OAAO,CAAG,QAAAA,CAAA,CAAoB,IAAnB,CAAAI,SAAS,CAAAlB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC7BiB,iBAAiB,CAACC,SAAS,CAAC,CAC9B,CAAC,CAED,MAAO,CAAEvB,IAAI,CAAES,OAAO,CAAEf,KAAK,CAAEyB,OAAQ,CAAC,CAC1C,CAAC,CAED;AACA,MAAO,MAAM,CAAAW,QAAQ,CAAG,QAAAA,CAAA,CAAuB,IAAtB,CAAAC,SAAS,CAAA1B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACxC,KAAM,CAACL,IAAI,CAAEQ,OAAO,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACe,KAAK,CAAEiB,QAAQ,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAEiC,eAAe,CAAEH,OAAO,CAAEI,WAAY,CAAC,CAAG/B,OAAO,CAAC,CAAC,CAE3D,KAAM,CAAAkD,UAAU,CAAG,cAAAA,CAAA,CAAoC,IAA7B,CAAAC,YAAY,CAAA5B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG0B,SAAS,CAChD,GAAI,CACFrB,UAAU,CAAC,IAAI,CAAC,CAChBC,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAd,GAAG,CAACgC,GAAG,CAAC,oBAAoBkB,YAAY,EAAE,CAAC,CAClEzB,OAAO,CAACX,QAAQ,CAACG,IAAI,CAAC,CACxB,CAAE,MAAOgB,GAAG,CAAE,KAAAkB,cAAA,CAAAC,mBAAA,CACZxB,QAAQ,CAAC,EAAAuB,cAAA,CAAAlB,GAAG,CAACnB,QAAQ,UAAAqC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAclC,IAAI,UAAAmC,mBAAA,iBAAlBA,mBAAA,CAAoBlC,OAAO,GAAIe,GAAG,CAACf,OAAO,EAAI,4BAA4B,CAAC,CACtF,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED9B,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACiC,WAAW,EAAID,eAAe,CAAE,CACnCoB,UAAU,CAAC,CAAC,CACd,CAAC,IAAM,IAAI,CAACnB,WAAW,EAAI,CAACD,eAAe,CAAE,CAC3C;AACAJ,OAAO,CAAC,IAAI,CAAC,CACbE,UAAU,CAAC,KAAK,CAAC,CACjBC,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAC,CAAE,CAACoB,SAAS,CAAEnB,eAAe,CAAEC,WAAW,CAAC,CAAC,CAE7C,KAAM,CAAAM,OAAO,CAAG,QAAAA,CAAA,CAA8B,IAA7B,CAAAc,YAAY,CAAA5B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG0B,SAAS,CACvCC,UAAU,CAACC,YAAY,CAAC,CAC1B,CAAC,CAED,MAAO,CAAEjC,IAAI,CAAES,OAAO,CAAEf,KAAK,CAAEyB,OAAQ,CAAC,CAC1C,CAAC,CAED;AACA,MAAO,MAAM,CAAAiB,cAAc,CAAIC,EAAE,EAAK,CACpC,KAAM,CAACrC,IAAI,CAAEQ,OAAO,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACe,KAAK,CAAEiB,QAAQ,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAEiC,eAAe,CAAEH,OAAO,CAAEI,WAAY,CAAC,CAAG/B,OAAO,CAAC,CAAC,CAE3DF,SAAS,CAAC,IAAM,CACd,GAAI,CAACyD,EAAE,CAAE,OAET,KAAM,CAAAC,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF5B,UAAU,CAAC,IAAI,CAAC,CAChBC,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAd,GAAG,CAACgC,GAAG,CAAC,iBAAiBsB,EAAE,EAAE,CAAC,CACrD7B,OAAO,CAACX,QAAQ,CAACG,IAAI,CAAC,CACxB,CAAE,MAAOgB,GAAG,CAAE,KAAAuB,cAAA,CAAAC,mBAAA,CACZ7B,QAAQ,CAAC,EAAA4B,cAAA,CAAAvB,GAAG,CAACnB,QAAQ,UAAA0C,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcvC,IAAI,UAAAwC,mBAAA,iBAAlBA,mBAAA,CAAoBvC,OAAO,GAAIe,GAAG,CAACf,OAAO,EAAI,6BAA6B,CAAC,CACvF,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,GAAI,CAACG,WAAW,EAAID,eAAe,CAAE,CACnC0B,gBAAgB,CAAC,CAAC,CACpB,CAAC,IAAM,IAAI,CAACzB,WAAW,EAAI,CAACD,eAAe,CAAE,CAC3C;AACAJ,OAAO,CAAC,IAAI,CAAC,CACbE,UAAU,CAAC,KAAK,CAAC,CACjBC,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAC,CAAE,CAAC0B,EAAE,CAAEzB,eAAe,CAAEC,WAAW,CAAC,CAAC,CAEtC,MAAO,CAAEb,IAAI,CAAES,OAAO,CAAEf,KAAM,CAAC,CACjC,CAAC,CAED,cAAe,CAAAX,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}