#!/usr/bin/env node

// Official MongoDB 6.x connection test following best practices
const { MongoClient } = require('mongodb');

console.log('Testing MongoDB 6.x connection following official documentation...\n');

const config = {
    host: 'localhost',
    port: 27017,
    database: 'haraka_email_logs'
};

// Test 1: Basic connection following MongoDB 6.x official documentation
async function testOfficialConnection() {
    console.log('1. Testing official MongoDB 6.x connection pattern...');

    const uri = `mongodb://${config.host}:${config.port}/${config.database}`;
    console.log('Connection URI:', uri);

    // Use minimal options as recommended by MongoDB 6.x docs
    const options = {
        connectTimeoutMS: 10000,
        socketTimeoutMS: 30000,
        maxPoolSize: 10
    };

    console.log('Connection options:', JSON.stringify(options, null, 2));

    let client;

    try {
        // Create MongoClient following official documentation
        client = new MongoClient(uri, options);

        // Connect to MongoDB
        console.log('Connecting to MongoDB...');
        await client.connect();

        // Get database reference
        const db = client.db(config.database);

        // Test connection with ping (recommended by docs)
        console.log('Testing connection with ping...');
        await db.admin().ping();

        console.log('✅ Successfully connected to MongoDB!');
        console.log('✅ Database accessed:', config.database);

        // Test a simple operation
        const collections = await db.listCollections().toArray();
        console.log('✅ Collections found:', collections.length);

        return true;

    } catch (error) {
        console.error('❌ Connection failed:', error.message);
        console.error('Error code:', error.code || 'N/A');
        console.error('Error name:', error.name || 'N/A');

        if (error.cause) {
            console.error('Cause:', error.cause.message);
        }

        return false;

    } finally {
        if (client) {
            try {
                await client.close();
                console.log('📝 Connection closed');
            } catch (closeErr) {
                console.error('Error closing connection:', closeErr.message);
            }
        }
    }
}

// Test 2: Test different timeout values
async function testTimeoutOptions() {
    console.log('\n2. Testing different timeout configurations...');

    const timeoutTests = [
        { name: 'Short timeout (5s)', connectTimeoutMS: 5000 },
        { name: 'Medium timeout (10s)', connectTimeoutMS: 10000 },
        { name: 'Long timeout (30s)', connectTimeoutMS: 30000 }
    ];

    for (const test of timeoutTests) {
        console.log(`\nTesting: ${test.name}`);

        const uri = `mongodb://${config.host}:${config.port}/${config.database}`;
        const options = {
            connectTimeoutMS: test.connectTimeoutMS,
            socketTimeoutMS: 30000,
            maxPoolSize: 10
        };

        let client;
        try {
            client = new MongoClient(uri, options);
            await client.connect();
            await client.db(config.database).admin().ping();
            console.log(`✅ ${test.name} - Success`);
        } catch (error) {
            console.log(`❌ ${test.name} - Failed: ${error.message}`);
        } finally {
            if (client) {
                try {
                    await client.close();
                } catch (closeErr) {
                    // Ignore close errors in test
                }
            }
        }
    }
}

async function main() {
    const success = await testOfficialConnection();

    if (success) {
        await testTimeoutOptions();
        console.log('\n✅ All tests completed successfully!');
    } else {
        console.log('\n❌ Basic connection test failed. Skipping additional tests.');
        process.exit(1);
    }
}

main().catch(console.error);
