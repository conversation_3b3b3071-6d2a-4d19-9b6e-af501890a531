# MongoDB Email Logging Setup for Haraka

Complete guide for setting up comprehensive email transaction logging to MongoDB for your Haraka email server.

## 🎯 **Overview**

This MongoDB logging system captures ALL email transactions in your Haraka server, providing:
- **Complete audit trail** of every email transaction
- **Performance metrics** and processing times
- **Security monitoring** with source IP tracking
- **Privacy protection** integration with existing header stripping
- **Retention management** with automatic data cleanup
- **Query tools** for analysis and reporting

## 📋 **Prerequisites**

### **1. MongoDB Installation**
```bash
# Install MongoDB on Ubuntu/Debian
sudo apt update
sudo apt install -y mongodb

# Or install MongoDB Community Edition
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt update
sudo apt install -y mongodb-org

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod

# Verify MongoDB is running
sudo systemctl status mongod
```

### **2. Node.js MongoDB Driver**
```bash
# Install MongoDB driver for Node.js
cd /path/to/haraka
npm install mongodb
```

## 🔧 **Installation Steps**

### **Step 1: Database Setup**
```bash
# Run the database schema setup script
node mongodb_schema.js
```

This creates:
- Database: `haraka_email_logs`
- Collections: `email_transactions`, `email_connections`, `email_errors`, `email_stats`
- Indexes for efficient querying
- TTL index for automatic data retention

### **Step 2: Plugin Configuration**
The plugin is already configured with these files:
- ✅ `plugins/mongodb_logger.js` - Main plugin
- ✅ `config/mongodb_logger.ini` - Configuration
- ✅ `config/plugins` - Plugin enabled

### **Step 3: Test Installation**
```bash
# Test MongoDB connection and setup
node test_mongodb_logging.js
```

### **Step 4: Restart Haraka**
```bash
# Restart Haraka to load the MongoDB logging plugin
sudo systemctl restart haraka
# or however you normally restart Haraka
```

## ⚙️ **Configuration Options**

### **Main Settings** (`config/mongodb_logger.ini`)
```ini
[main]
enabled=true                    # Enable/disable logging
host=localhost                  # MongoDB host
port=27017                     # MongoDB port
database=haraka_email_logs     # Database name
log_all_transactions=true      # Log all email types
log_headers=true               # Include email headers
sanitize_headers=true          # Remove private IPs from headers
retention_days=365             # Auto-delete after days (0=keep forever)
batch_size=100                 # Batch insert size
batch_timeout=5000             # Batch timeout (ms)
```

### **Privacy Protection**
```ini
[privacy]
strip_private_ips=true         # Remove private IPs from logs
strip_internal_headers=true    # Remove internal headers
exclude_headers=received-spf,authentication-results,x-originating-ip
```

## 📊 **Data Structure**

### **Email Transaction Document**
```javascript
{
  transaction_id: "uuid-string",
  timestamp: ISODate("2025-07-20T12:00:00Z"),
  connection: {
    remote_ip: "*************",
    remote_host: "client.example.com",
    local_ip: "********",
    local_port: 25,
    tls: { enabled: true, cipher: "...", version: "TLSv1.2" }
  },
  helo: "client.example.com",
  mail_from: {
    address: "<EMAIL>",
    original: "<<EMAIL>>",
    host: "example.com"
  },
  rcpt_to: [{
    address: "<EMAIL>",
    original: "<<EMAIL>>",
    host: "domain.com",
    timestamp: ISODate("...")
  }],
  headers: {
    "from": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "Email Subject",
    "message-id": "<<EMAIL>>"
  },
  message: {
    size: 1024,
    subject: "Email Subject",
    message_id: "<<EMAIL>>"
  },
  authentication: {
    spf: { result: "pass", domain: "example.com" },
    dkim: { result: "pass", domains: ["example.com"] },
    dmarc: { result: "pass", policy: "quarantine" }
  },
  status: "delivered", // in_progress, queued, relayed, delivered, bounced, rejected, deferred
  rejection_reason: null,
  processing_time: 1500, // milliseconds
  timestamps: {
    connect: ISODate("..."),
    helo: ISODate("..."),
    mail_from: ISODate("..."),
    data_start: ISODate("..."),
    data_complete: ISODate("..."),
    queue: ISODate("..."),
    disconnect: ISODate("...")
  }
}
```

## 🔍 **Monitoring & Analysis**

### **Real-time Monitoring**
```bash
# View recent transactions
node mongodb_queries.js

# Monitor MongoDB logs
tail -f /var/log/mongodb/mongod.log

# Monitor Haraka logs for MongoDB plugin
grep "MongoDB Logger" /var/log/haraka.log
```

### **Common Queries**
```javascript
// Recent transactions
db.email_transactions.find().sort({timestamp: -1}).limit(10)

// Emails from specific IP
db.email_transactions.find({"connection.remote_ip": "*************"})

// Rejected emails
db.email_transactions.find({status: "rejected"})

// Performance analysis
db.email_transactions.aggregate([
  {$group: {_id: null, avg_time: {$avg: "$processing_time"}}}
])
```

## 📈 **Dashboard Integration Ready**

The logged data is structured for easy dashboard integration:

### **Key Metrics Available**
- **Volume**: Emails per hour/day/month
- **Status Distribution**: Delivered, bounced, rejected ratios
- **Performance**: Processing times, bottlenecks
- **Security**: Source IP analysis, rejection patterns
- **Authentication**: SPF/DKIM/DMARC success rates

### **Recommended Dashboard Tools**
- **MongoDB Compass** - Native MongoDB GUI
- **Grafana** with MongoDB data source
- **Custom web dashboard** using the logged data
- **Elasticsearch + Kibana** (with MongoDB river)

## 🔒 **Privacy & Security**

### **Privacy Protection Features**
- ✅ **IP Sanitization**: Private IPs replaced with `[PRIVATE_IP]`
- ✅ **Header Stripping**: Integrates with existing header stripping plugins
- ✅ **Home IP Protection**: Your dynamic IP replaced with `[HOME_SERVER_IP]`
- ✅ **Configurable Exclusions**: Skip sensitive headers entirely

### **Security Considerations**
- **Database Access**: Secure MongoDB with authentication
- **Network Security**: Bind MongoDB to localhost only
- **Data Retention**: Automatic cleanup after configured period
- **Backup Strategy**: Regular MongoDB backups recommended

## 🛠️ **Maintenance**

### **Regular Tasks**
```bash
# Check database size
mongo haraka_email_logs --eval "db.stats()"

# Manual cleanup (if needed)
mongo haraka_email_logs --eval "db.email_transactions.deleteMany({timestamp: {\$lt: new Date(Date.now() - 30*24*60*60*1000)}})"

# Backup database
mongodump --db haraka_email_logs --out /backup/mongodb/

# Monitor plugin performance
grep "MongoDB Logger" /var/log/haraka.log | tail -20
```

### **Performance Tuning**
- **Batch Size**: Increase for high-volume servers
- **Indexes**: Add custom indexes for specific queries
- **Retention**: Reduce retention period for storage savings
- **Sharding**: Consider for very high-volume deployments

## 🚀 **Next Steps**

1. **✅ Complete Installation**: Follow all setup steps above
2. **📧 Send Test Emails**: Verify logging is working
3. **📊 Analyze Data**: Use query tools to explore logs
4. **🎨 Build Dashboard**: Create web interface for monitoring
5. **⚡ Optimize Performance**: Tune settings based on usage

Your Haraka server now has enterprise-level email transaction logging! 📊🛡️
