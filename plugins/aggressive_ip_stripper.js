// Aggressive IP Stripper Plugin for Haraka
// Strips ANY header containing the dynamic home server IP address

const dns = require('dns');

exports.register = function () {
    const plugin = this;

    plugin.loginfo('Aggressive IP Stripper plugin loading...');

    // Load configuration
    plugin.load_config();

    // Initialize variables
    plugin.home_ip = null;
    plugin.ip_refresh_timer = null;
    plugin.last_ip_check = 0;

    // Start IP monitoring
    plugin.start_ip_monitoring();

    // Register hooks at multiple points to catch all headers
    plugin.register_hook('connect', 'log_connection');
    plugin.register_hook('data_post', 'strip_ip_headers');
    plugin.register_hook('queue', 'strip_ip_headers');
    plugin.register_hook('queue_outbound', 'strip_ip_headers');

    plugin.loginfo('Aggressive IP Stripper plugin registered successfully');
}

exports.load_config = function () {
    const plugin = this;

    plugin.cfg = plugin.config.get('aggressive_ip_stripper.ini', {
        booleans: [
            '+main.enabled',
            '+main.aggressive_mode',
            '+main.debug_logging',
            '+main.backup_headers',
            '+logging.log_dns_resolution',
            '+logging.log_header_actions'
        ]
    }, function() {
        plugin.load_config();
    });

    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (!plugin.cfg.main.hostname) plugin.cfg.main.hostname = 'hallocktest6.tmv.co.il';
    if (!plugin.cfg.main.ip_refresh_interval) plugin.cfg.main.ip_refresh_interval = 300;
    if (!plugin.cfg.main.domain) plugin.cfg.main.domain = 'mosh.wtf';
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (plugin.cfg.main.aggressive_mode === undefined) plugin.cfg.main.aggressive_mode = true;
    if (plugin.cfg.main.debug_logging === undefined) plugin.cfg.main.debug_logging = true;
    if (plugin.cfg.main.backup_headers === undefined) plugin.cfg.main.backup_headers = false;

    if (!plugin.cfg.logging) plugin.cfg.logging = {};
    if (plugin.cfg.logging.log_dns_resolution === undefined) plugin.cfg.logging.log_dns_resolution = true;
    if (plugin.cfg.logging.log_header_actions === undefined) plugin.cfg.logging.log_header_actions = true;

    plugin.loginfo('Configuration loaded - Hostname: ' + plugin.cfg.main.hostname + ', Domain: ' + plugin.cfg.main.domain);
}

exports.start_ip_monitoring = function () {
    const plugin = this;

    if (!plugin.cfg.main.enabled) {
        plugin.loginfo('Aggressive IP Stripper is disabled');
        return;
    }

    plugin.loginfo('Starting IP monitoring for ' + plugin.cfg.main.hostname + ' every ' + plugin.cfg.main.ip_refresh_interval + ' seconds');

    // Initial IP resolution
    plugin.resolve_home_ip();

    // Set up periodic IP refresh
    plugin.ip_refresh_timer = setInterval(function() {
        plugin.resolve_home_ip();
    }, plugin.cfg.main.ip_refresh_interval * 1000);
}

exports.resolve_home_ip = function () {
    const plugin = this;
    const hostname = plugin.cfg.main.hostname;

    if (plugin.cfg.logging.log_dns_resolution) {
        plugin.logdebug('Resolving IP for ' + hostname);
    }

    dns.resolve4(hostname, function(err, addresses) {
        if (err) {
            plugin.logwarn('Could not resolve ' + hostname + ': ' + err.message);
            return;
        }

        if (addresses && addresses.length > 0) {
            const new_ip = addresses[0];

            if (plugin.home_ip !== new_ip) {
                if (plugin.home_ip) {
                    plugin.loginfo('IP changed for ' + hostname + ': ' + plugin.home_ip + ' -> ' + new_ip);
                } else {
                    plugin.loginfo('Initial IP for ' + hostname + ': ' + new_ip);
                }
                plugin.home_ip = new_ip;
            } else if (plugin.cfg.logging.log_dns_resolution) {
                plugin.logdebug('IP unchanged for ' + hostname + ': ' + new_ip);
            }

            plugin.last_ip_check = Date.now();
        }
    });
}

exports.log_connection = function (next, connection) {
    if (this.cfg.main.debug_logging) {
        const remote_ip = connection.remote?.ip;
        this.loginfo('Connection from: ' + remote_ip + ' (Home IP: ' + this.home_ip + ')');
    }
    next();
}

exports.strip_ip_headers = function (next, connection) {
    const plugin = this;
    const txn = connection?.transaction;

    // Check if plugin is enabled
    if (!plugin.cfg.main.enabled) {
        return next();
    }

    if (!txn) {
        if (plugin.cfg.main.debug_logging) {
            plugin.logdebug('No transaction found, skipping header stripping');
        }
        return next();
    }

    if (!plugin.home_ip) {
        if (plugin.cfg.main.debug_logging) {
            plugin.logdebug('Home IP not resolved yet, skipping header stripping');
        }
        return next();
    }

    if (plugin.cfg.main.debug_logging) {
        plugin.loginfo('=== AGGRESSIVE IP STRIPPER RUNNING ===');
        plugin.loginfo('Home IP to strip: ' + plugin.home_ip);
        plugin.loginfo('Hostname: ' + plugin.cfg.main.hostname);
    }
    
    const header_obj = txn.header;
    if (!header_obj) {
        plugin.logwarn('No header object found');
        return next();
    }
    
    let headers_stripped = 0;
    
    // Get all headers using different methods
    let all_headers = [];
    
    // Method 1: Try to get headers directly
    if (header_obj.headers) {
        for (const name in header_obj.headers) {
            const values = header_obj.headers[name];
            if (Array.isArray(values)) {
                for (const value of values) {
                    all_headers.push({name: name, value: value});
                }
            } else {
                all_headers.push({name: name, value: values});
            }
        }
    }
    
    // Method 2: Try header_list if available
    if (header_obj.header_list && header_obj.header_list.length > 0) {
        for (const header of header_obj.header_list) {
            if (header.name && header.value) {
                all_headers.push({name: header.name, value: header.value});
            }
        }
    }
    
    plugin.loginfo('Found ' + all_headers.length + ' headers to check');
    
    // Check each header for our IP and other patterns
    for (const header of all_headers) {
        if (!header.value || typeof header.value !== 'string') continue;

        let should_strip = false;
        let reason = '';

        // Check for home IP
        if (plugin.home_ip && header.value.includes(plugin.home_ip)) {
            should_strip = true;
            reason = 'contains home IP: ' + plugin.home_ip;
        }

        // Check for hostname
        if (!should_strip && plugin.cfg.main.hostname && header.value.includes(plugin.cfg.main.hostname)) {
            should_strip = true;
            reason = 'contains hostname: ' + plugin.cfg.main.hostname;
        }

        // Check for additional IP patterns from config
        if (!should_strip && plugin.cfg.ip_patterns) {
            for (const pattern_name in plugin.cfg.ip_patterns) {
                try {
                    const regex = new RegExp(plugin.cfg.ip_patterns[pattern_name]);
                    if (regex.test(header.value)) {
                        should_strip = true;
                        reason = 'matches pattern: ' + pattern_name;
                        break;
                    }
                } catch (err) {
                    plugin.logwarn('Invalid regex pattern ' + pattern_name + ': ' + err.message);
                }
            }
        }

        // Check for headers to always strip
        if (!should_strip && plugin.cfg.headers_to_always_strip) {
            const header_name_lower = header.name.toLowerCase();
            for (const strip_header in plugin.cfg.headers_to_always_strip) {
                if (header_name_lower === strip_header.toLowerCase()) {
                    should_strip = true;
                    reason = 'configured to always strip: ' + strip_header;
                    break;
                }
            }
        }

        if (should_strip) {
            if (plugin.cfg.logging.log_header_actions) {
                plugin.loginfo('STRIPPING HEADER: ' + header.name + ' (' + reason + ')');
                if (plugin.cfg.main.debug_logging) {
                    plugin.loginfo('Header value: ' + header.value.substring(0, 100) + '...');
                }
            }

            try {
                // Try different removal methods
                if (header_obj.remove) {
                    header_obj.remove(header.name);
                } else if (header_obj.remove_header) {
                    header_obj.remove_header(header.name);
                } else if (header_obj.headers && header_obj.headers[header.name]) {
                    delete header_obj.headers[header.name];
                }
                headers_stripped++;
            } catch (err) {
                plugin.logwarn('Failed to remove header ' + header.name + ': ' + err.message);
            }
        }
    }
    
    if (headers_stripped > 0) {
        if (plugin.cfg.logging.log_header_actions) {
            plugin.loginfo('=== STRIPPED ' + headers_stripped + ' HEADERS ===');
        }
    } else if (plugin.cfg.main.debug_logging) {
        plugin.logdebug('=== NO HEADERS STRIPPED ===');
    }

    next();
}

exports.shutdown = function () {
    const plugin = this;

    if (plugin.ip_refresh_timer) {
        clearInterval(plugin.ip_refresh_timer);
        plugin.ip_refresh_timer = null;
        plugin.loginfo('Stopped IP monitoring timer');
    }

    plugin.loginfo('Aggressive IP Stripper plugin shutting down');
}
