# Dynamic IP Updater Plugin for Haraka

This plugin automatically monitors and updates the Haraka relay ACL configuration when your dynamic IP address changes. It's specifically designed for scenarios where you have a server with a dynamic IP (like `hallocktest6.tmv.co.il`) that needs to relay mail through your Haraka server.

## Features

- **Automatic IP monitoring**: Periodically resolves your hostname to check for IP changes
- **Automatic ACL updates**: Updates `relay_acl_allow` file when IP changes are detected
- **Configuration backup**: Optionally backs up the old configuration before making changes
- **Configurable intervals**: Set how often to check for IP changes
- **Manual trigger**: Includes a script for immediate IP updates
- **Logging**: Comprehensive logging of all operations

## Installation

The plugin is already installed and configured in your Haraka setup:

1. **Plugin file**: `plugins/dynamic_ip_updater.js`
2. **Configuration**: `config/dynamic_ip_updater.ini`
3. **Enabled in**: `config/plugins`

## Configuration

Edit `config/dynamic_ip_updater.ini` to customize the behavior:

```ini
[main]
; Enable or disable the dynamic IP updater
enabled=true

; Hostname to monitor for IP changes
hostname=hallocktest6.tmv.co.il

; How often to check for IP changes (in seconds)
; Default: 300 (5 minutes)
check_interval=300

; Path to the relay ACL file (relative to config directory)
relay_acl_file=relay_acl_allow

; Whether to backup the old configuration file before updating
backup_old_config=true
```

## Usage

### Automatic Operation

Once Haraka is restarted, the plugin will:

1. Start monitoring `hallocktest6.tmv.co.il` every 5 minutes (configurable)
2. Compare the resolved IP with the current configuration
3. Update `config/relay_acl_allow` if the IP has changed
4. Create a backup of the old configuration (if enabled)
5. Log all operations

### Manual Updates

You can force an immediate IP check and update using the provided script:

```bash
# Check and update IP immediately
node update_relay_ip.js

# Show help
node update_relay_ip.js --help

# Update without creating backup
node update_relay_ip.js --no-backup
```

### Testing

Test the plugin setup:

```bash
node test_dynamic_ip.js
```

## Starting the Plugin

1. **Restart Haraka** to load the new plugin:
   ```bash
   # Stop Haraka (method depends on how you're running it)
   sudo systemctl stop haraka
   # or
   pkill -f haraka
   
   # Start Haraka
   sudo systemctl start haraka
   # or
   haraka -c /path/to/your/config
   ```

2. **Check the logs** for plugin initialization:
   ```bash
   tail -f /var/log/haraka.log
   # Look for messages like:
   # [INFO] Dynamic IP Updater plugin initialized
   # [INFO] Starting IP monitoring for hallocktest6.tmv.co.il every 300 seconds
   ```

## Log Messages

The plugin generates several types of log messages:

- **INFO**: Normal operations (IP checks, updates, initialization)
- **WARN**: Non-critical issues (backup failures, config reload issues)
- **ERROR**: Critical issues (DNS resolution failures, file write errors)
- **DEBUG**: Detailed information (unchanged IP notifications)

Example log messages:
```
[INFO] Dynamic IP Updater plugin initialized in master process
[INFO] Starting IP monitoring for hallocktest6.tmv.co.il every 300 seconds
[INFO] Initial IP for hallocktest6.tmv.co.il: *************
[INFO] IP change detected for hallocktest6.tmv.co.il: ************* -> *************
[INFO] Successfully updated relay_acl_allow with new IP: *************
```

## Troubleshooting

### Plugin Not Loading
- Check that `dynamic_ip_updater` is listed in `config/plugins`
- Verify the plugin file exists at `plugins/dynamic_ip_updater.js`
- Check Haraka logs for any syntax errors

### DNS Resolution Issues
- Verify that `hallocktest6.tmv.co.il` resolves correctly: `nslookup hallocktest6.tmv.co.il`
- Check network connectivity from the Haraka server
- Review DNS server configuration

### File Permission Issues
- Ensure Haraka has write permissions to the config directory
- Check that the `relay_acl_allow` file is writable
- Verify backup directory permissions

### Configuration Not Reloading
- The plugin attempts to reload the relay configuration automatically
- If this fails, you may need to restart Haraka for changes to take effect
- Check logs for reload success/failure messages

## Cron Integration

For additional reliability, you can set up a cron job to run the manual update script:

```bash
# Edit crontab
crontab -e

# Add a line to check every 10 minutes
*/10 * * * * cd /path/to/haraka && node update_relay_ip.js >> /var/log/haraka-ip-update.log 2>&1
```

## Security Considerations

- The plugin only updates the specific relay ACL file
- Backups are created before any changes
- DNS resolution uses the system's configured DNS servers
- No external dependencies or network connections beyond DNS

## Files Created/Modified

- `plugins/dynamic_ip_updater.js` - Main plugin file
- `config/dynamic_ip_updater.ini` - Plugin configuration
- `config/plugins` - Updated to include the plugin
- `config/relay_acl_allow` - Updated automatically when IP changes
- `config/relay_acl_allow.backup.*` - Backup files (if enabled)

## Support

The plugin is designed to be robust and handle common failure scenarios gracefully. If you encounter issues:

1. Check the Haraka logs for error messages
2. Run the test script: `node test_dynamic_ip.js`
3. Try the manual update script: `node update_relay_ip.js`
4. Verify DNS resolution manually: `nslookup hallocktest6.tmv.co.il`
