#!/bin/bash

# Haraka Dashboard Setup Script
# This script sets up the Haraka Email Dashboard

set -e

echo "🚀 Haraka Email Dashboard Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Check prerequisites
print_header "Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi
print_status "Node.js $(node --version) ✓"

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi
print_status "npm $(npm --version) ✓"

# Check MongoDB
if ! command -v mongosh &> /dev/null && ! command -v mongo &> /dev/null; then
    print_warning "MongoDB client not found. Make sure MongoDB is accessible."
else
    print_status "MongoDB client found ✓"
fi

# Check Docker (optional)
if command -v docker &> /dev/null; then
    print_status "Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) ✓"
    DOCKER_AVAILABLE=true
else
    print_warning "Docker not found. Manual installation will be used."
    DOCKER_AVAILABLE=false
fi

# Installation method selection
echo ""
print_header "Installation Options:"
echo "1. Docker Compose (Recommended)"
echo "2. Manual Installation"
echo "3. Development Setup"

read -p "Choose installation method (1-3): " INSTALL_METHOD

case $INSTALL_METHOD in
    1)
        if [ "$DOCKER_AVAILABLE" = false ]; then
            print_error "Docker is not available. Please install Docker first or choose manual installation."
            exit 1
        fi
        
        print_header "Setting up with Docker Compose..."
        
        # Check if docker-compose.yml exists
        if [ ! -f "docker-compose.yml" ]; then
            print_error "docker-compose.yml not found. Make sure you're in the correct directory."
            exit 1
        fi
        
        # Start services
        print_status "Starting services..."
        docker-compose up -d
        
        # Wait for services to be ready
        print_status "Waiting for services to start..."
        sleep 10
        
        # Check health
        print_status "Checking service health..."
        if curl -f http://localhost:3001/api/health &> /dev/null; then
            print_status "Backend API is healthy ✓"
        else
            print_warning "Backend API health check failed"
        fi
        
        if curl -f http://localhost:3000 &> /dev/null; then
            print_status "Frontend is accessible ✓"
        else
            print_warning "Frontend accessibility check failed"
        fi
        
        print_status "Docker setup complete!"
        echo ""
        echo "🌐 Access your dashboard:"
        echo "   Frontend: http://localhost:3000"
        echo "   API: http://localhost:3001/api/health"
        echo ""
        echo "📋 Useful commands:"
        echo "   View logs: docker-compose logs -f"
        echo "   Stop services: docker-compose down"
        echo "   Restart: docker-compose restart"
        ;;
        
    2)
        print_header "Manual Installation..."
        
        # Install backend dependencies
        print_status "Installing backend dependencies..."
        cd backend
        npm install
        
        # Copy environment file
        if [ ! -f ".env" ]; then
            print_status "Creating environment configuration..."
            cp .env .env.local
            print_warning "Please edit backend/.env.local with your MongoDB connection details"
        fi
        
        # Install frontend dependencies
        print_status "Installing frontend dependencies..."
        cd ../frontend
        npm install
        
        # Build frontend
        print_status "Building frontend..."
        npm run build
        
        print_status "Manual installation complete!"
        echo ""
        echo "🚀 To start the services:"
        echo "   Backend: cd backend && npm start"
        echo "   Frontend: cd frontend && npm start"
        echo ""
        echo "📋 Configuration:"
        echo "   Edit backend/.env.local for MongoDB settings"
        echo "   Frontend will be available at http://localhost:3000"
        echo "   Backend API will be available at http://localhost:3001"
        ;;
        
    3)
        print_header "Development Setup..."
        
        # Install backend dependencies
        print_status "Installing backend dependencies..."
        cd backend
        npm install
        
        # Install nodemon for development
        if ! npm list -g nodemon &> /dev/null; then
            print_status "Installing nodemon globally..."
            npm install -g nodemon
        fi
        
        # Copy environment file
        if [ ! -f ".env" ]; then
            print_status "Creating development environment..."
            cp .env .env.development
            print_warning "Please edit backend/.env.development for development settings"
        fi
        
        # Install frontend dependencies
        print_status "Installing frontend dependencies..."
        cd ../frontend
        npm install
        
        print_status "Development setup complete!"
        echo ""
        echo "🛠️  Development commands:"
        echo "   Backend dev: cd backend && npm run dev"
        echo "   Frontend dev: cd frontend && npm start"
        echo ""
        echo "📋 Development features:"
        echo "   • Hot reload enabled"
        echo "   • Source maps for debugging"
        echo "   • Development error overlay"
        echo "   • API proxy configured"
        ;;
        
    *)
        print_error "Invalid option selected"
        exit 1
        ;;
esac

echo ""
print_header "Setup Summary:"
echo "✅ Prerequisites checked"
echo "✅ Dependencies installed"
echo "✅ Configuration created"

if [ "$INSTALL_METHOD" = "1" ]; then
    echo "✅ Docker services started"
fi

echo ""
print_status "Haraka Email Dashboard setup completed successfully!"
echo ""
print_header "Next Steps:"
echo "1. Ensure your Haraka server is logging to MongoDB"
echo "2. Verify MongoDB connection settings"
echo "3. Access the dashboard and check for data"
echo "4. Review the README.md for additional configuration options"

if [ "$INSTALL_METHOD" != "1" ]; then
    echo ""
    print_warning "Remember to start both backend and frontend services!"
fi
