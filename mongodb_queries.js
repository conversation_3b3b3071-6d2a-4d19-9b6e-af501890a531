#!/usr/bin/env node

// MongoDB Query Helper for Haraka Email Logs
// Provides common queries for analyzing email traffic

const { MongoClient } = require('mongodb');

const config = {
    host: 'localhost',
    port: 27017,
    database: 'haraka_email_logs'
};

const connection_string = `mongodb://${config.host}:${config.port}/${config.database}`;

class EmailLogAnalyzer {
    constructor() {
        this.client = null;
        this.db = null;
    }
    
    async connect() {
        this.client = await MongoClient.connect(connection_string, {
            connectTimeoutMS: 5000,
            socketTimeoutMS: 30000
        });
        this.db = this.client.db(config.database);
        console.log('📊 Connected to email logs database');
    }
    
    async disconnect() {
        if (this.client) {
            await this.client.close();
            console.log('📝 Database connection closed');
        }
    }
    
    async getRecentTransactions(limit = 10) {
        console.log(`\n📋 Recent ${limit} transactions:`);
        console.log('=' .repeat(80));
        
        const transactions = this.db.collection('email_transactions');
        const recent = await transactions.find()
            .sort({ timestamp: -1 })
            .limit(limit)
            .toArray();
        
        recent.forEach(tx => {
            const from = tx.mail_from?.address || 'N/A';
            const to = tx.rcpt_to?.[0]?.address || 'N/A';
            const status = tx.status || 'unknown';
            const time = tx.timestamp ? tx.timestamp.toISOString() : 'N/A';
            const ip = tx.connection?.remote_ip || 'N/A';
            
            console.log(`${time} | ${status.padEnd(10)} | ${ip.padEnd(15)} | ${from} → ${to}`);
        });
        
        return recent;
    }
    
    async getStatusSummary(hours = 24) {
        console.log(`\n📊 Email status summary (last ${hours} hours):`);
        console.log('=' .repeat(50));
        
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const transactions = this.db.collection('email_transactions');
        
        const pipeline = [
            { $match: { timestamp: { $gte: since } } },
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
        ];
        
        const results = await transactions.aggregate(pipeline).toArray();
        
        let total = 0;
        results.forEach(result => {
            console.log(`${result._id.padEnd(15)}: ${result.count}`);
            total += result.count;
        });
        
        console.log(`${'TOTAL'.padEnd(15)}: ${total}`);
        return results;
    }
    
    async getTopSenders(limit = 10, hours = 24) {
        console.log(`\n👤 Top ${limit} senders (last ${hours} hours):`);
        console.log('=' .repeat(60));
        
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const transactions = this.db.collection('email_transactions');
        
        const pipeline = [
            { $match: { timestamp: { $gte: since }, 'mail_from.address': { $exists: true } } },
            { $group: { _id: '$mail_from.address', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: limit }
        ];
        
        const results = await transactions.aggregate(pipeline).toArray();
        
        results.forEach((result, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${result._id.padEnd(40)} (${result.count} emails)`);
        });
        
        return results;
    }
    
    async getTopRecipients(limit = 10, hours = 24) {
        console.log(`\n📧 Top ${limit} recipients (last ${hours} hours):`);
        console.log('=' .repeat(60));
        
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const transactions = this.db.collection('email_transactions');
        
        const pipeline = [
            { $match: { timestamp: { $gte: since } } },
            { $unwind: '$rcpt_to' },
            { $group: { _id: '$rcpt_to.address', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: limit }
        ];
        
        const results = await transactions.aggregate(pipeline).toArray();
        
        results.forEach((result, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${result._id.padEnd(40)} (${result.count} emails)`);
        });
        
        return results;
    }
    
    async getSourceIPs(limit = 10, hours = 24) {
        console.log(`\n🌐 Top ${limit} source IPs (last ${hours} hours):`);
        console.log('=' .repeat(60));
        
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const transactions = this.db.collection('email_transactions');
        
        const pipeline = [
            { $match: { timestamp: { $gte: since }, 'connection.remote_ip': { $exists: true } } },
            { $group: { _id: '$connection.remote_ip', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: limit }
        ];
        
        const results = await transactions.aggregate(pipeline).toArray();
        
        results.forEach((result, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${result._id.padEnd(15)} (${result.count} connections)`);
        });
        
        return results;
    }
    
    async getRejectedEmails(limit = 10, hours = 24) {
        console.log(`\n❌ Recent ${limit} rejected emails (last ${hours} hours):`);
        console.log('=' .repeat(80));
        
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const transactions = this.db.collection('email_transactions');
        
        const rejected = await transactions.find({
            timestamp: { $gte: since },
            status: 'rejected'
        })
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();
        
        rejected.forEach(tx => {
            const from = tx.mail_from?.address || 'N/A';
            const ip = tx.connection?.remote_ip || 'N/A';
            const reason = tx.rejection_reason || 'No reason specified';
            const time = tx.timestamp ? tx.timestamp.toISOString() : 'N/A';
            
            console.log(`${time} | ${ip.padEnd(15)} | ${from}`);
            console.log(`  Reason: ${reason}`);
            console.log('');
        });
        
        return rejected;
    }
    
    async getPerformanceStats(hours = 24) {
        console.log(`\n⚡ Performance statistics (last ${hours} hours):`);
        console.log('=' .repeat(50));
        
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const transactions = this.db.collection('email_transactions');
        
        const pipeline = [
            { $match: { 
                timestamp: { $gte: since },
                processing_time: { $exists: true, $gt: 0 }
            }},
            { $group: {
                _id: null,
                avg_processing_time: { $avg: '$processing_time' },
                min_processing_time: { $min: '$processing_time' },
                max_processing_time: { $max: '$processing_time' },
                total_emails: { $sum: 1 }
            }}
        ];
        
        const results = await transactions.aggregate(pipeline).toArray();
        
        if (results.length > 0) {
            const stats = results[0];
            console.log(`Total emails processed: ${stats.total_emails}`);
            console.log(`Average processing time: ${Math.round(stats.avg_processing_time)}ms`);
            console.log(`Minimum processing time: ${stats.min_processing_time}ms`);
            console.log(`Maximum processing time: ${stats.max_processing_time}ms`);
        } else {
            console.log('No performance data available');
        }
        
        return results;
    }
}

async function main() {
    const analyzer = new EmailLogAnalyzer();
    
    try {
        await analyzer.connect();
        
        // Run various analyses
        await analyzer.getRecentTransactions(10);
        await analyzer.getStatusSummary(24);
        await analyzer.getTopSenders(5);
        await analyzer.getTopRecipients(5);
        await analyzer.getSourceIPs(5);
        await analyzer.getRejectedEmails(5);
        await analyzer.getPerformanceStats(24);
        
    } catch (error) {
        console.error('❌ Error running analysis:', error.message);
    } finally {
        await analyzer.disconnect();
    }
}

// Check command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    console.log('MongoDB Email Log Analyzer');
    console.log('');
    console.log('Usage: node mongodb_queries.js [options]');
    console.log('');
    console.log('This script analyzes email transaction logs stored in MongoDB');
    console.log('and provides various statistics and reports.');
    console.log('');
    console.log('Make sure MongoDB is running and contains email log data.');
    process.exit(0);
}

// Run the analysis
main();
