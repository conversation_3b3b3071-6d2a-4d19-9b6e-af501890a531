{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import axios from'axios';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext();export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[token,setToken]=useState(localStorage.getItem('auth_token'));// Configure axios defaults\nuseEffect(()=>{if(token){axios.defaults.headers.common['Authorization']=`Bearer ${token}`;}else{delete axios.defaults.headers.common['Authorization'];}},[token]);// Check if user is authenticated on app load\nuseEffect(()=>{const checkAuth=async()=>{if(token){try{const response=await axios.get('/api/auth/me');setUser(response.data.user);}catch(error){console.error('Auth check failed:',error);logout();}}setLoading(false);};checkAuth();},[token]);const login=async(username,password)=>{try{const response=await axios.post('/api/auth/login',{username,password});const{token:newToken,user:userData}=response.data;setToken(newToken);setUser(userData);localStorage.setItem('auth_token',newToken);axios.defaults.headers.common['Authorization']=`Bearer ${newToken}`;return{success:true};}catch(error){var _error$response,_error$response$data;console.error('Login failed:',error);return{success:false,message:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.error)||'Login failed'};}};const logout=async()=>{try{if(token){await axios.post('/api/auth/logout');}}catch(error){console.error('Logout error:',error);}finally{setToken(null);setUser(null);localStorage.removeItem('auth_token');delete axios.defaults.headers.common['Authorization'];}};const changePassword=async(currentPassword,newPassword)=>{try{const response=await axios.post('/api/admin/change-password',{currentPassword,newPassword});return{success:true,message:response.data.message};}catch(error){var _error$response2,_error$response2$data;console.error('Change password failed:',error);return{success:false,message:((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.error)||'Failed to change password'};}};const value={user,token,loading,login,logout,changePassword,isAuthenticated:!!user,isAdmin:(user===null||user===void 0?void 0:user.role)==='admin'};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsx", "_jsx", "AuthContext", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "console", "logout", "login", "username", "password", "post", "newToken", "userData", "setItem", "success", "_error$response", "_error$response$data", "message", "removeItem", "changePassword", "currentPassword", "newPassword", "_error$response2", "_error$response2$data", "value", "isAuthenticated", "isAdmin", "role", "Provider"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('auth_token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/auth/me');\n          setUser(response.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (username, password) => {\n    try {\n      const response = await axios.post('/api/auth/login', {\n        username,\n        password\n      });\n\n      const { token: newToken, user: userData } = response.data;\n      \n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('auth_token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n      return { success: true };\n    } catch (error) {\n      console.error('Login failed:', error);\n      return {\n        success: false,\n        message: error.response?.data?.error || 'Login failed'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      if (token) {\n        await axios.post('/api/auth/logout');\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setToken(null);\n      setUser(null);\n      localStorage.removeItem('auth_token');\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      const response = await axios.post('/api/admin/change-password', {\n        currentPassword,\n        newPassword\n      });\n\n      return { success: true, message: response.data.message };\n    } catch (error) {\n      console.error('Change password failed:', error);\n      return {\n        success: false,\n        message: error.response?.data?.error || 'Failed to change password'\n      };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    changePassword,\n    isAuthenticated: !!user,\n    isAdmin: user?.role === 'admin'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC7E,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE1B,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAAC,CAAC,CAEnC,MAAO,MAAM,CAAAQ,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGR,UAAU,CAACM,WAAW,CAAC,CACvC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgB,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAACkB,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAEtE;AACAlB,SAAS,CAAC,IAAM,CACd,GAAIe,KAAK,CAAE,CACTd,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUN,KAAK,EAAE,CACpE,CAAC,IAAM,CACL,MAAO,CAAAd,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACvD,CACF,CAAC,CAAE,CAACN,KAAK,CAAC,CAAC,CAEX;AACAf,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAIP,KAAK,CAAE,CACT,GAAI,CACF,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAACuB,GAAG,CAAC,cAAc,CAAC,CAChDZ,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACd,IAAI,CAAC,CAC7B,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CE,MAAM,CAAC,CAAC,CACV,CACF,CACAd,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAEDQ,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACP,KAAK,CAAC,CAAC,CAEX,KAAM,CAAAc,KAAK,CAAG,KAAAA,CAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC1C,GAAI,CACF,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAAC+B,IAAI,CAAC,iBAAiB,CAAE,CACnDF,QAAQ,CACRC,QACF,CAAC,CAAC,CAEF,KAAM,CAAEhB,KAAK,CAAEkB,QAAQ,CAAEtB,IAAI,CAAEuB,QAAS,CAAC,CAAGX,QAAQ,CAACE,IAAI,CAEzDT,QAAQ,CAACiB,QAAQ,CAAC,CAClBrB,OAAO,CAACsB,QAAQ,CAAC,CACjBjB,YAAY,CAACkB,OAAO,CAAC,YAAY,CAAEF,QAAQ,CAAC,CAC5ChC,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUY,QAAQ,EAAE,CAErE,MAAO,CAAEG,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOV,KAAK,CAAE,KAAAW,eAAA,CAAAC,oBAAA,CACdX,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,MAAO,CACLU,OAAO,CAAE,KAAK,CACdG,OAAO,CAAE,EAAAF,eAAA,CAAAX,KAAK,CAACH,QAAQ,UAAAc,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBZ,IAAI,UAAAa,oBAAA,iBAApBA,oBAAA,CAAsBZ,KAAK,GAAI,cAC1C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAE,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF,GAAIb,KAAK,CAAE,CACT,KAAM,CAAAd,KAAK,CAAC+B,IAAI,CAAC,kBAAkB,CAAC,CACtC,CACF,CAAE,MAAON,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,OAAS,CACRV,QAAQ,CAAC,IAAI,CAAC,CACdJ,OAAO,CAAC,IAAI,CAAC,CACbK,YAAY,CAACuB,UAAU,CAAC,YAAY,CAAC,CACrC,MAAO,CAAAvC,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACvD,CACF,CAAC,CAED,KAAM,CAAAoB,cAAc,CAAG,KAAAA,CAAOC,eAAe,CAAEC,WAAW,GAAK,CAC7D,GAAI,CACF,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAAC+B,IAAI,CAAC,4BAA4B,CAAE,CAC9DU,eAAe,CACfC,WACF,CAAC,CAAC,CAEF,MAAO,CAAEP,OAAO,CAAE,IAAI,CAAEG,OAAO,CAAEhB,QAAQ,CAACE,IAAI,CAACc,OAAQ,CAAC,CAC1D,CAAE,MAAOb,KAAK,CAAE,KAAAkB,gBAAA,CAAAC,qBAAA,CACdlB,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CACLU,OAAO,CAAE,KAAK,CACdG,OAAO,CAAE,EAAAK,gBAAA,CAAAlB,KAAK,CAACH,QAAQ,UAAAqB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBnB,IAAI,UAAAoB,qBAAA,iBAApBA,qBAAA,CAAsBnB,KAAK,GAAI,2BAC1C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAoB,KAAK,CAAG,CACZnC,IAAI,CACJI,KAAK,CACLF,OAAO,CACPgB,KAAK,CACLD,MAAM,CACNa,cAAc,CACdM,eAAe,CAAE,CAAC,CAACpC,IAAI,CACvBqC,OAAO,CAAE,CAAArC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsC,IAAI,IAAK,OAC1B,CAAC,CAED,mBACE9C,IAAA,CAACC,WAAW,CAAC8C,QAAQ,EAACJ,KAAK,CAAEA,KAAM,CAAApC,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}