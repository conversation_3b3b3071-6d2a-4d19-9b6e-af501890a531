# Systemd service template for Haraka IP Updater
# Copy this file to /etc/systemd/system/haraka-ip-updater.service
# and modify the paths as needed

[Unit]
Description=Haraka Dynamic IP Updater
After=network.target
Wants=network.target

[Service]
Type=oneshot
User=haraka
Group=haraka
WorkingDirectory=/home/<USER>/haraka
ExecStart=/usr/bin/node /home/<USER>/haraka/update_relay_ip.js
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target

# To use this service:
# 1. Copy to /etc/systemd/system/haraka-ip-updater.service
# 2. Edit paths to match your setup
# 3. sudo systemctl daemon-reload
# 4. sudo systemctl enable haraka-ip-updater.service
#
# To run periodically, create a timer:
# /etc/systemd/system/haraka-ip-updater.timer
#
# [Unit]
# Description=Run Haraka IP Updater every 10 minutes
# Requires=haraka-ip-updater.service
#
# [Timer]
# OnCalendar=*:0/10
# Persistent=true
#
# [Install]
# WantedBy=timers.target
#
# Then: sudo systemctl enable haraka-ip-updater.timer
