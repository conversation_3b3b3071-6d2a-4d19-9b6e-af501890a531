import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Mail, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  ArrowRight,
  Activity
} from 'lucide-react';
import { useStats, useTransactions } from '../hooks/useApi';
import { formatDate, formatRelativeTime, getStatusBadge, formatEmail } from '../utils/formatters';

const Dashboard = () => {
  const { data: stats, loading: statsLoading, error: statsError } = useStats('24h');
  const { data: recentTransactions, loading: transactionsLoading } = useTransactions({ 
    limit: 5, 
    sortBy: 'timestamp', 
    sortOrder: 'desc' 
  });

  const StatCard = ({ title, value, icon: Icon, color, change, loading }) => (
    <div className="card p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <div className="flex items-baseline">
            <p className="text-2xl font-semibold text-gray-900">
              {loading ? '...' : value}
            </p>
            {change && (
              <p className={`ml-2 text-sm ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change > 0 ? '+' : ''}{change}%
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const StatusCard = ({ status, count, loading }) => {
    const statusInfo = getStatusBadge(status);
    return (
      <div className="card p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500 capitalize">{status}</p>
            <p className="text-xl font-semibold text-gray-900">
              {loading ? '...' : count}
            </p>
          </div>
          <span className={`badge ${statusInfo.className}`}>
            {statusInfo.label}
          </span>
        </div>
      </div>
    );
  };

  if (statsError) {
    return (
      <div className="text-center py-12">
        <XCircle className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading dashboard</h3>
        <p className="mt-1 text-sm text-gray-500">{statsError}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Email Server Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Monitor your Haraka email server performance and transactions
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <div className="flex items-center text-sm text-gray-500">
            <Activity className="h-4 w-4 text-green-500 mr-1" />
            Server Online
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Transactions"
          value={stats?.totalTransactions || 0}
          icon={Mail}
          color="bg-primary-500"
          loading={statsLoading}
        />
        <StatCard
          title="Last 24 Hours"
          value={stats?.recentTransactions || 0}
          icon={TrendingUp}
          color="bg-green-500"
          loading={statsLoading}
        />
        <StatCard
          title="Success Rate"
          value={stats?.recentTransactions > 0 ? 
            `${Math.round(((stats?.statusStats?.find(s => s._id === 'delivered')?.count || 0) / stats.recentTransactions) * 100)}%` : 
            '0%'
          }
          icon={CheckCircle}
          color="bg-blue-500"
          loading={statsLoading}
        />
        <StatCard
          title="Avg Processing"
          value="< 1s"
          icon={Clock}
          color="bg-purple-500"
          loading={statsLoading}
        />
      </div>

      {/* Status Breakdown */}
      {stats?.statusStats && stats.statusStats.length > 0 && (
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Transaction Status (Last 24h)</h3>
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-5">
            {stats.statusStats.map((status) => (
              <StatusCard
                key={status._id}
                status={status._id}
                count={status.count}
                loading={statsLoading}
              />
            ))}
          </div>
        </div>
      )}

      {/* Recent Transactions */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Recent Transactions</h3>
            <Link
              to="/transactions"
              className="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-500"
            >
              View all
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
        </div>
        <div className="overflow-hidden">
          {transactionsLoading ? (
            <div className="p-6 text-center">
              <div className="loading-spinner mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading transactions...</p>
            </div>
          ) : recentTransactions?.transactions?.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {recentTransactions.transactions.map((transaction) => (
                <div key={transaction._id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <Mail className="h-5 w-5 text-gray-400" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {formatEmail(transaction.mail_from?.address)}
                          </p>
                          <p className="text-sm text-gray-500 truncate">
                            to {transaction.rcpt_to?.length > 0 ? 
                              formatEmail(transaction.rcpt_to[0]?.address) : 
                              'No recipients'
                            }
                          </p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-sm text-gray-600 truncate">
                          {transaction.message?.subject || 'No subject'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-500">
                          {formatRelativeTime(transaction.timestamp)}
                        </p>
                        <p className="text-xs text-gray-400">
                          {formatDate(transaction.timestamp, 'HH:mm:ss')}
                        </p>
                      </div>
                      <span className={`badge ${getStatusBadge(transaction.status).className}`}>
                        {getStatusBadge(transaction.status).label}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center">
              <Mail className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions</h3>
              <p className="mt-1 text-sm text-gray-500">
                No email transactions found in the last 24 hours.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Top Senders & Recipients */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Top Senders */}
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Senders (24h)</h3>
          {stats?.topSenders?.length > 0 ? (
            <div className="space-y-3">
              {stats.topSenders.slice(0, 5).map((sender, index) => (
                <div key={sender._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">
                      {index + 1}
                    </span>
                    <span className="text-sm text-gray-900 truncate">
                      {formatEmail(sender._id)}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-500">
                    {sender.count}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No data available</p>
          )}
        </div>

        {/* Top Recipients */}
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Recipients (24h)</h3>
          {stats?.topRecipients?.length > 0 ? (
            <div className="space-y-3">
              {stats.topRecipients.slice(0, 5).map((recipient, index) => (
                <div key={recipient._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium">
                      {index + 1}
                    </span>
                    <span className="text-sm text-gray-900 truncate">
                      {formatEmail(recipient._id)}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-500">
                    {recipient.count}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No data available</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
