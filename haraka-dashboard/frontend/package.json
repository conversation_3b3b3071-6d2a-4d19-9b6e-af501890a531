{"name": "haraka-dashboard-frontend", "version": "1.0.0", "description": "Frontend dashboard for Haraka Email Server", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "clsx": "^2.0.0", "connect-mongo": "^5.1.0", "date-fns": "^2.30.0", "express-session": "^1.18.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "tailwindcss": "^3.3.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}