# Haraka Email Dashboard - Apache2 Virtual Host Configuration
# This configuration serves the React frontend and proxies API requests to the Node.js backend

<VirtualHost *:80>
    ServerAdmin <EMAIL>
    ServerName panel.mosh.wtf
    ServerAlias www.panel.mosh.wtf
    DocumentRoot /var/www/panel.mosh.wtf/public_html
    
    # Enable mod_rewrite for React Router
    RewriteEngine On
    
    # Security headers
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "no-referrer-when-downgrade"
    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src 'self' http://s3.tmv.co.il:3001"
    
    # Proxy API requests to Node.js backend
    ProxyPreserveHost On
    ProxyRequests Off
    
    # API proxy configuration
    ProxyPass /api/ http://localhost:3001/api/
    ProxyPassReverse /api/ http://localhost:3001/api/
    
    # Handle React Router - serve index.html for all non-file requests
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule . /index.html [L]
    
    # Cache static assets
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </LocationMatch>
    
    # Don't cache HTML files
    <LocationMatch "\.html$">
        ExpiresActive On
        ExpiresDefault "access plus 0 seconds"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
    </LocationMatch>
    
    # Gzip compression
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png|ico)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # Directory configuration
    <Directory /var/www/haraka-dashboard.mosh.wtf>
        Options -Indexes +FollowSymLinks
        AllowOverride None
        Require all granted
        
        # Additional security
        <Files "*.php">
            Require all denied
        </Files>
        
        <Files "*.json">
            <RequireAll>
                Require all granted
                Require not env bad_bot
            </RequireAll>
        </Files>
    </Directory>
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/haraka-dashboard.mosh.wtf_error.log
    CustomLog ${APACHE_LOG_DIR}/haraka-dashboard.mosh.wtf_access.log combined
    
    # Optional: Rate limiting (requires mod_evasive)
    # DOSHashTableSize    2048
    # DOSPageCount        20
    # DOSPageInterval     1
    # DOSSiteCount        50
    # DOSSiteInterval     1
    # DOSBlockingPeriod   600
</VirtualHost>

# SSL Configuration (recommended for production)
# Uncomment and configure after obtaining SSL certificate
#<VirtualHost *:443>
#    ServerAdmin <EMAIL>
#    ServerName haraka-dashboard.mosh.wtf
#    ServerAlias www.haraka-dashboard.mosh.wtf
#    DocumentRoot /var/www/haraka-dashboard.mosh.wtf
#    
#    # SSL Configuration
#    SSLEngine on
#    SSLCertificateFile /path/to/your/certificate.crt
#    SSLCertificateKeyFile /path/to/your/private.key
#    SSLCertificateChainFile /path/to/your/chain.crt
#    
#    # Modern SSL configuration
#    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
#    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
#    SSLHonorCipherOrder off
#    SSLSessionTickets off
#    
#    # HSTS (optional)
#    Header always set Strict-Transport-Security "max-age=63072000"
#    
#    # Same configuration as HTTP version above...
#    # (Copy all the RewriteEngine, ProxyPass, Directory, etc. directives)
#</VirtualHost>

# Redirect HTTP to HTTPS (uncomment when SSL is configured)
#<VirtualHost *:80>
#    ServerName haraka-dashboard.mosh.wtf
#    ServerAlias www.haraka-dashboard.mosh.wtf
#    Redirect permanent / https://haraka-dashboard.mosh.wtf/
#</VirtualHost>
