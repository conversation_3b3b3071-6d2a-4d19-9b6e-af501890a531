[{"/home/<USER>/haraka/haraka-dashboard/frontend/src/index.js": "1", "/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js": "2", "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js": "3", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Dashboard.js": "4", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js": "5", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js": "6", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Statistics.js": "7", "/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js": "8", "/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js": "9", "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/ProtectedRoute.js": "10", "/home/<USER>/haraka/haraka-dashboard/frontend/src/contexts/AuthContext.js": "11", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Login.js": "12"}, {"size": 254, "mtime": 1753041207794, "results": "13", "hashOfConfig": "14"}, {"size": 1502, "mtime": 1753045159382, "results": "15", "hashOfConfig": "14"}, {"size": 6513, "mtime": 1753045209423, "results": "16", "hashOfConfig": "14"}, {"size": 10851, "mtime": 1753041343965, "results": "17", "hashOfConfig": "14"}, {"size": 17064, "mtime": 1753041472105, "results": "18", "hashOfConfig": "14"}, {"size": 10472, "mtime": 1753041520655, "results": "19", "hashOfConfig": "14"}, {"size": 10652, "mtime": 1753041569425, "results": "20", "hashOfConfig": "14"}, {"size": 3847, "mtime": 1753042915190, "results": "21", "hashOfConfig": "14"}, {"size": 4095, "mtime": 1753041297108, "results": "22", "hashOfConfig": "14"}, {"size": 1072, "mtime": 1753045144106, "results": "23", "hashOfConfig": "14"}, {"size": 2952, "mtime": 1753045105389, "results": "24", "hashOfConfig": "14"}, {"size": 5818, "mtime": 1753045131913, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "esj15b", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/haraka/haraka-dashboard/frontend/src/index.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js", ["62", "63"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Dashboard.js", ["64"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js", ["65", "66"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js", ["67", "68"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Statistics.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js", ["69", "70", "71"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/contexts/AuthContext.js", ["72"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Login.js", ["73"], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 10, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 10, "endColumn": 11}, {"ruleId": "74", "severity": 1, "message": "78", "line": 11, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 11, "endColumn": 9}, {"ruleId": "74", "severity": 1, "message": "79", "line": 9, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 9, "endColumn": 14}, {"ruleId": "74", "severity": 1, "message": "80", "line": 9, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 9, "endColumn": 11}, {"ruleId": "81", "severity": 1, "message": "82", "line": 49, "column": 6, "nodeType": "83", "endLine": 49, "endColumn": 15, "suggestions": "84"}, {"ruleId": "74", "severity": 1, "message": "85", "line": 10, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 10, "endColumn": 7}, {"ruleId": "74", "severity": 1, "message": "86", "line": 11, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 11, "endColumn": 8}, {"ruleId": "81", "severity": 1, "message": "87", "line": 53, "column": 6, "nodeType": "83", "endLine": 53, "endColumn": 11, "suggestions": "88"}, {"ruleId": "81", "severity": 1, "message": "89", "line": 88, "column": 6, "nodeType": "83", "endLine": 88, "endColumn": 8, "suggestions": "90"}, {"ruleId": "81", "severity": 1, "message": "91", "line": 118, "column": 6, "nodeType": "83", "endLine": 118, "endColumn": 17, "suggestions": "92"}, {"ruleId": "81", "severity": 1, "message": "93", "line": 44, "column": 6, "nodeType": "83", "endLine": 44, "endColumn": 13, "suggestions": "94"}, {"ruleId": "74", "severity": 1, "message": "95", "line": 3, "column": 16, "nodeType": "76", "messageId": "77", "endLine": 3, "endColumn": 20}, "no-unused-vars", "'Activity' is defined but never used.", "Identifier", "unusedVar", "'Server' is defined but never used.", "'AlertCircle' is defined but never used.", "'Calendar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'refetch'. Either include it or remove the dependency array.", "ArrayExpression", ["96"], "'User' is defined but never used.", "'Globe' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["97"], "React Hook useEffect has a missing dependency: 'fetchTransactions'. Either include it or remove the dependency array.", ["98"], "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", ["99"], "React Hook useEffect has a missing dependency: 'logout'. Either include it or remove the dependency array.", ["100"], "'Lock' is defined but never used.", {"desc": "101", "fix": "102"}, {"desc": "103", "fix": "104"}, {"desc": "105", "fix": "106"}, {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, "Update the dependencies array to be: [filters, refetch]", {"range": "111", "text": "112"}, "Update the dependencies array to be: [fetchData, url]", {"range": "113", "text": "114"}, "Update the dependencies array to be: [fetchTransactions]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [fetchStats, timeframe]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [logout, token]", {"range": "119", "text": "120"}, [990, 999], "[filters, refetch]", [1246, 1251], "[fetchData, url]", [2208, 2210], "[fetchTransactions]", [2976, 2987], "[fetchStats, timeframe]", [1190, 1197], "[logout, token]"]