[main]
; Enable or disable the aggressive IP stripper
enabled=true

; Hostname to resolve for dynamic IP (your home server)
hostname=hallocktest6.tmv.co.il

; How often to refresh the IP address (in seconds)
; Default: 300 (5 minutes)
ip_refresh_interval=300

; Your domain name (used for logging and identification)
domain=mosh.wtf

; Enable aggressive mode - strip ANY header containing the resolved IP
aggressive_mode=true

; Enable detailed logging for debugging
debug_logging=true

; Backup old headers before stripping (for debugging)
backup_headers=false

[headers_to_always_strip]
; Additional headers to always strip regardless of IP content
; Format: header_name=1
; Example:
; X-Source-IP=1
; X-Client-IP=1

[ip_patterns]
; Additional IP patterns to strip (regex patterns)
; These will be checked in addition to the resolved hostname IP
; Format: pattern_name=regex_pattern
; Example:
; private_192=192\.168\.\d+\.\d+
; private_10=10\.\d+\.\d+\.\d+
; private_172=172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+

[logging]
; Log level for this plugin (debug, info, warn, error)
level=info

; Log IP resolution attempts
log_dns_resolution=true

; Log header stripping actions
log_header_actions=true
