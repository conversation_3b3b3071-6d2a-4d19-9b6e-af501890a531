#!/usr/bin/env node

// MongoDB Schema Setup for Haraka Email Logging
// This script creates the database, collections, and indexes for email logging

const { MongoClient } = require('mongodb');

const config = {
    host: 'localhost',
    port: 27017,
    database: 'haraka_email_logs',
    // username: '',
    // password: '',
    retention_days: 365
};

const connection_string = 'mongodb://' + 
    (config.username ? config.username + ':' + config.password + '@' : '') +
    config.host + ':' + config.port + '/' + config.database;

console.log('MongoDB Schema Setup for Haraka Email Logging');
console.log('==============================================\n');

async function setupDatabase() {
    let client;
    
    try {
        console.log('Connecting to MongoDB...');
        client = await MongoClient.connect(connection_string, {
            connectTimeoutMS: 5000,
            socketTimeoutMS: 30000
        });
        
        const db = client.db(config.database);
        console.log('✅ Connected to database:', config.database);
        
        // Create collections
        await createCollections(db);
        
        // Create indexes
        await createIndexes(db);
        
        // Insert sample data structure documentation
        await insertSampleData(db);
        
        console.log('\n✅ Database setup completed successfully!');
        
    } catch (error) {
        console.error('❌ Error setting up database:', error.message);
        process.exit(1);
    } finally {
        if (client) {
            await client.close();
            console.log('📝 Database connection closed');
        }
    }
}

async function createCollections(db) {
    console.log('\n📁 Creating collections...');
    
    const collections = [
        'email_transactions',
        'email_connections', 
        'email_errors',
        'email_stats'
    ];
    
    for (const collectionName of collections) {
        try {
            await db.createCollection(collectionName);
            console.log('✅ Created collection:', collectionName);
        } catch (error) {
            if (error.code === 48) {
                console.log('ℹ️  Collection already exists:', collectionName);
            } else {
                console.error('❌ Error creating collection', collectionName + ':', error.message);
            }
        }
    }
}

async function createIndexes(db) {
    console.log('\n🔍 Creating indexes...');
    
    const transactions = db.collection('email_transactions');
    
    const indexes = [
        // Single field indexes
        { timestamp: -1 },
        { transaction_id: 1 },
        { 'mail_from.address': 1 },
        { 'rcpt_to.address': 1 },
        { 'connection.remote_ip': 1 },
        { status: 1 },
        
        // Compound indexes for common queries
        { timestamp: -1, status: 1 },
        { 'connection.remote_ip': 1, timestamp: -1 },
        { 'mail_from.address': 1, timestamp: -1 },
        { 'rcpt_to.address': 1, timestamp: -1 },
        { status: 1, timestamp: -1 },
        
        // Text index for searching
        { 'message.subject': 'text', 'mail_from.address': 'text', 'rcpt_to.address': 'text' }
    ];
    
    for (const index of indexes) {
        try {
            await transactions.createIndex(index);
            console.log('✅ Created index:', JSON.stringify(index));
        } catch (error) {
            console.error('❌ Error creating index:', error.message);
        }
    }
    
    // Create TTL index for automatic data retention
    if (config.retention_days > 0) {
        try {
            const ttl_seconds = config.retention_days * 24 * 60 * 60;
            await transactions.createIndex(
                { timestamp: 1 }, 
                { expireAfterSeconds: ttl_seconds }
            );
            console.log('✅ Created TTL index with', config.retention_days, 'days retention');
        } catch (error) {
            console.error('❌ Error creating TTL index:', error.message);
        }
    }
}

async function insertSampleData(db) {
    console.log('\n📄 Inserting sample data structure documentation...');
    
    const transactions = db.collection('email_transactions');
    
    const sampleTransaction = {
        _id: 'SAMPLE_DOCUMENT_STRUCTURE',
        transaction_id: 'uuid-string',
        timestamp: new Date(),
        connection: {
            remote_ip: '*************',
            remote_host: 'client.example.com',
            local_ip: '********',
            local_port: 25,
            tls: {
                enabled: true,
                cipher: 'ECDHE-RSA-AES256-GCM-SHA384',
                version: 'TLSv1.2'
            }
        },
        helo: 'client.example.com',
        mail_from: {
            address: '<EMAIL>',
            original: '<<EMAIL>>',
            host: 'example.com'
        },
        rcpt_to: [
            {
                address: '<EMAIL>',
                original: '<<EMAIL>>',
                host: 'domain.com',
                timestamp: new Date()
            }
        ],
        headers: {
            'from': '<EMAIL>',
            'to': '<EMAIL>',
            'subject': 'Test Email',
            'date': 'Mon, 20 Jul 2025 10:00:00 +0000',
            'message-id': '<<EMAIL>>'
        },
        message: {
            size: 1024,
            subject: 'Test Email',
            message_id: '<<EMAIL>>'
        },
        authentication: {
            spf: {
                result: 'pass',
                domain: 'example.com'
            },
            dkim: {
                result: 'pass',
                domains: ['example.com']
            },
            dmarc: {
                result: 'pass',
                policy: 'quarantine'
            }
        },
        status: 'delivered', // in_progress, queued, relayed, delivered, bounced, rejected, deferred, disconnected
        rejection_reason: null,
        bounce_reason: null,
        defer_reason: null,
        processing_time: 1500, // milliseconds
        errors: [],
        timestamps: {
            connect: new Date(),
            helo: new Date(),
            mail_from: new Date(),
            data_start: new Date(),
            data_complete: new Date(),
            queue: new Date(),
            disconnect: new Date()
        }
    };
    
    try {
        await transactions.replaceOne(
            { _id: 'SAMPLE_DOCUMENT_STRUCTURE' },
            sampleTransaction,
            { upsert: true }
        );
        console.log('✅ Sample document structure inserted');
    } catch (error) {
        console.error('❌ Error inserting sample data:', error.message);
    }
}

// Run the setup
setupDatabase();
