// Dynamic IP Updater Plugin for Haraka
// Automatically updates relay_acl_allow when dynamic IP changes

const dns = require('dns');
const fs = require('fs');
const path = require('path');

exports.register = function () {
    const plugin = this;

    plugin.load_config();

    // Register hooks first
    plugin.register_hook('init_master', 'init_master');
    plugin.register_hook('init_child', 'init_child');

    // Start the IP monitoring process after a short delay
    setTimeout(function() {
        plugin.start_ip_monitoring();
    }, 1000);
}

exports.load_config = function () {
    const plugin = this;

    plugin.cfg = plugin.config.get('dynamic_ip_updater.ini', {
        booleans: [
            '+main.enabled',
            '+main.backup_old_config'
        ]
    }, function() {
        plugin.load_config();
    });

    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (!plugin.cfg.main.hostname) plugin.cfg.main.hostname = 'hallocktest6.tmv.co.il';
    if (!plugin.cfg.main.check_interval) plugin.cfg.main.check_interval = 300; // 5 minutes
    if (!plugin.cfg.main.relay_acl_file) plugin.cfg.main.relay_acl_file = 'relay_acl_allow';
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (plugin.cfg.main.backup_old_config === undefined) plugin.cfg.main.backup_old_config = true;

    plugin.current_ip = null;
    plugin.check_timer = null;
}

exports.init_master = function (next) {
    this.loginfo('Dynamic IP Updater plugin initialized in master process');
    next();
}

exports.init_child = function (next) {
    this.loginfo('Dynamic IP Updater plugin initialized in child process');
    next();
}

exports.start_ip_monitoring = function () {
    const plugin = this;

    if (!plugin.cfg.main.enabled) {
        plugin.loginfo('Dynamic IP monitoring is disabled');
        return;
    }

    plugin.loginfo('Starting IP monitoring for ' + plugin.cfg.main.hostname + ' every ' + plugin.cfg.main.check_interval + ' seconds');

    // Initial check
    plugin.check_ip_change();

    // Set up periodic checks
    plugin.check_timer = setInterval(function() {
        plugin.check_ip_change();
    }, plugin.cfg.main.check_interval * 1000);
}

exports.check_ip_change = function () {
    const plugin = this;
    const hostname = plugin.cfg.main.hostname;

    dns.resolve4(hostname, function(err, addresses) {
        if (err) {
            plugin.logerror('Failed to resolve ' + hostname + ': ' + err.message);
            return;
        }

        if (!addresses || addresses.length === 0) {
            plugin.logerror('No IP addresses found for ' + hostname);
            return;
        }

        const new_ip = addresses[0]; // Take the first IP if multiple

        if (plugin.current_ip === null) {
            // First run - just store the current IP
            plugin.current_ip = new_ip;
            plugin.loginfo('Initial IP for ' + hostname + ': ' + new_ip);
            
            // Verify the current config matches
            plugin.verify_current_config(new_ip);
        } else if (plugin.current_ip !== new_ip) {
            // IP has changed
            plugin.loginfo('IP change detected for ' + hostname + ': ' + plugin.current_ip + ' -> ' + new_ip);
            plugin.update_relay_acl(new_ip);
            plugin.current_ip = new_ip;
        } else {
            plugin.logdebug('IP unchanged for ' + hostname + ': ' + new_ip);
        }
    });
}

exports.verify_current_config = function (expected_ip) {
    const acl_file_path = path.join(this.config.root_path, this.cfg.main.relay_acl_file);
    
    fs.readFile(acl_file_path, 'utf8', (err, data) => {
        if (err) {
            this.logwarn(`Could not read ${acl_file_path}: ${err.message}`);
            this.loginfo('Will create the file on next IP change');
            return;
        }
        
        const lines = data.split('\n').map(line => line.trim()).filter(line => line && !line.startsWith('#'));
        const current_cidr = `${expected_ip}/32`;
        
        if (!lines.includes(current_cidr)) {
            this.logwarn(`Current IP ${expected_ip} not found in relay ACL, updating...`);
            this.update_relay_acl(expected_ip);
        } else {
            this.loginfo(`Current IP ${expected_ip} is correctly configured in relay ACL`);
        }
    });
}

exports.update_relay_acl = function (new_ip) {
    const acl_file_path = path.join(this.config.root_path, this.cfg.main.relay_acl_file);
    const hostname = this.cfg.main.hostname;
    const new_cidr = `${new_ip}/32`;
    
    // Backup existing file if enabled
    if (this.cfg.main.backup_old_config) {
        this.backup_config_file(acl_file_path);
    }
    
    // Create new content
    const header = `# Only allow ${hostname} to relay through this server\n# Auto-updated by dynamic_ip_updater plugin\n`;
    const content = header + new_cidr + '\n';
    
    fs.writeFile(acl_file_path, content, 'utf8', (err) => {
        if (err) {
            this.logerror(`Failed to update ${acl_file_path}: ${err.message}`);
            return;
        }
        
        this.loginfo(`Successfully updated ${acl_file_path} with new IP: ${new_ip}`);
        
        // Reload the relay plugin configuration
        this.reload_relay_config();
    });
}

exports.backup_config_file = function (file_path) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backup_path = `${file_path}.backup.${timestamp}`;
    
    fs.copyFile(file_path, backup_path, (err) => {
        if (err) {
            this.logwarn(`Failed to backup ${file_path}: ${err.message}`);
        } else {
            this.loginfo(`Backed up ${file_path} to ${backup_path}`);
        }
    });
}

exports.reload_relay_config = function () {
    try {
        // Try to reload the relay plugin's ACL configuration
        const relay_plugin = this.server?.plugins?.relay;
        if (relay_plugin && typeof relay_plugin.load_acl_allow === 'function') {
            relay_plugin.load_acl_allow();
            this.loginfo('Reloaded relay ACL configuration');
        } else {
            this.logwarn('Could not reload relay plugin configuration - restart may be required');
        }
    } catch (e) {
        this.logwarn(`Error reloading relay configuration: ${e.message}`);
    }
}

exports.shutdown = function () {
    if (this.check_timer) {
        clearInterval(this.check_timer);
        this.check_timer = null;
        this.loginfo('Stopped IP monitoring timer');
    }
}
