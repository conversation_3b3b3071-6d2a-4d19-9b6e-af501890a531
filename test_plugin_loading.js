#!/usr/bin/env node

// Test script to verify plugin loading
const fs = require('fs');
const path = require('path');

console.log('Testing Plugin Loading...\n');

// Test if we can require the plugins
try {
    console.log('1. Testing dynamic_ip_updater plugin:');
    const dynamic_plugin = require('./plugins/dynamic_ip_updater.js');
    
    if (typeof dynamic_plugin.register === 'function') {
        console.log('   ✅ Plugin has register function');
    } else {
        console.log('   ❌ Plugin missing register function');
    }
    
    if (typeof dynamic_plugin.load_config === 'function') {
        console.log('   ✅ Plugin has load_config function');
    } else {
        console.log('   ❌ Plugin missing load_config function');
    }
    
    console.log('   ✅ dynamic_ip_updater plugin loaded successfully');
    
} catch (err) {
    console.log('   ❌ Error loading dynamic_ip_updater plugin:', err.message);
}

try {
    console.log('\n2. Testing strip_relay_headers plugin:');
    const strip_plugin = require('./plugins/strip_relay_headers.js');
    
    if (typeof strip_plugin.register === 'function') {
        console.log('   ✅ Plugin has register function');
    } else {
        console.log('   ❌ Plugin missing register function');
    }
    
    if (typeof strip_plugin.load_config === 'function') {
        console.log('   ✅ Plugin has load_config function');
    } else {
        console.log('   ❌ Plugin missing load_config function');
    }
    
    console.log('   ✅ strip_relay_headers plugin loaded successfully');
    
} catch (err) {
    console.log('   ❌ Error loading strip_relay_headers plugin:', err.message);
}

// Check plugin configuration files
console.log('\n3. Testing configuration files:');

const configs = [
    'config/dynamic_ip_updater.ini',
    'config/strip_relay_headers.ini'
];

configs.forEach(configFile => {
    if (fs.existsSync(configFile)) {
        console.log(`   ✅ ${configFile} exists`);
    } else {
        console.log(`   ❌ ${configFile} missing`);
    }
});

// Check plugins file
console.log('\n4. Testing plugins configuration:');
const pluginsFile = 'config/plugins';
if (fs.existsSync(pluginsFile)) {
    const content = fs.readFileSync(pluginsFile, 'utf8');
    
    if (content.includes('dynamic_ip_updater')) {
        console.log('   ✅ dynamic_ip_updater listed in plugins');
    } else {
        console.log('   ❌ dynamic_ip_updater NOT listed in plugins');
    }
    
    if (content.includes('strip_relay_headers')) {
        console.log('   ✅ strip_relay_headers listed in plugins');
    } else {
        console.log('   ❌ strip_relay_headers NOT listed in plugins');
    }
} else {
    console.log('   ❌ config/plugins file missing');
}

console.log('\n✅ Plugin loading test completed!');
console.log('\nIf all tests pass, the plugins should work with Haraka.');
console.log('The issue might be with Haraka\'s plugin loading mechanism or configuration.');
console.log('\nTry starting Haraka with more verbose logging to see detailed error messages.');
