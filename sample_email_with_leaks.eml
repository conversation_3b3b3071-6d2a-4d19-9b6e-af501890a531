Return-Path: <<EMAIL>>
Received: from [*************] (helo=hallocktest6.tmv.co.il)
	by mosh.wtf (Haraka/2.8.27) with ESMTP id 12345678-1234-1234-1234-123456789012
	for <<EMAIL>>; Mon, 20 Jul 2025 10:00:00 +0000
X-Originating-IP: *************
X-Forwarded-For: *************, *************
X-Real-IP: *************
Message-ID: <<EMAIL>>
Date: Mon, 20 Jul 2025 10:00:00 +0000
From: <EMAIL>
To: <EMAIL>
Subject: Test Email with IP Leaks
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

This is a test email that contains headers revealing the home server IP address.
The header stripping plugin should remove the problematic headers.
