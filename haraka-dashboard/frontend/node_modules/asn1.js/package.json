{"name": "asn1.js", "version": "5.4.1", "description": "ASN.1 encoder and decoder", "main": "lib/asn1.js", "scripts": {"lint-2560": "eslint --fix rfc/2560/*.js rfc/2560/test/*.js", "lint-5280": "eslint --fix rfc/5280/*.js rfc/5280/test/*.js", "lint": "eslint --fix lib/*.js lib/**/*.js lib/**/**/*.js && npm run lint-2560 && npm run lint-5280", "test": "mocha --reporter spec test/*-test.js && cd rfc/2560 && npm i && npm test && cd ../../rfc/5280 && npm i && npm test && cd ../../ && npm run lint"}, "repository": {"type": "git", "url": "**************:indutny/asn1.js"}, "keywords": ["asn.1", "der"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/asn1.js/issues"}, "homepage": "https://github.com/indutny/asn1.js", "devDependencies": {"eslint": "^4.10.0", "mocha": "^7.0.0"}, "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "safer-buffer": "^2.1.0"}}