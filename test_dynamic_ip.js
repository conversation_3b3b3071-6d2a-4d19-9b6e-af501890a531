#!/usr/bin/env node

// Test script for the dynamic IP updater plugin
const dns = require('dns');
const fs = require('fs');
const path = require('path');

const hostname = 'hallocktest6.tmv.co.il';
const config_dir = './config';
const acl_file = path.join(config_dir, 'relay_acl_allow');

console.log('Testing Dynamic IP Updater functionality...\n');

// Test DNS resolution
console.log(`1. Testing DNS resolution for ${hostname}:`);
dns.resolve4(hostname, (err, addresses) => {
    if (err) {
        console.error(`   ❌ DNS resolution failed: ${err.message}`);
        return;
    }
    
    if (!addresses || addresses.length === 0) {
        console.error(`   ❌ No IP addresses found for ${hostname}`);
        return;
    }
    
    const ip = addresses[0];
    console.log(`   ✅ Resolved ${hostname} to: ${ip}`);
    
    // Test reading current ACL file
    console.log('\n2. Testing current relay ACL file:');
    fs.readFile(acl_file, 'utf8', (err, data) => {
        if (err) {
            console.log(`   ⚠️  Could not read ${acl_file}: ${err.message}`);
        } else {
            console.log(`   ✅ Current ACL file content:`);
            console.log('   ' + data.split('\n').map(line => `   ${line}`).join('\n'));
            
            // Check if current IP is in the file
            const lines = data.split('\n').map(line => line.trim()).filter(line => line && !line.startsWith('#'));
            const expected_cidr = `${ip}/32`;
            
            if (lines.includes(expected_cidr)) {
                console.log(`   ✅ Current IP ${ip} is correctly configured`);
            } else {
                console.log(`   ⚠️  Current IP ${ip} is NOT in the ACL file`);
                console.log(`   Expected to find: ${expected_cidr}`);
                console.log(`   Found entries: ${lines.join(', ')}`);
            }
        }
        
        // Test plugin configuration
        console.log('\n3. Testing plugin configuration:');
        const config_file = path.join(config_dir, 'dynamic_ip_updater.ini');
        fs.readFile(config_file, 'utf8', (err, data) => {
            if (err) {
                console.error(`   ❌ Could not read ${config_file}: ${err.message}`);
            } else {
                console.log(`   ✅ Plugin configuration file exists`);
                console.log('   Configuration preview:');
                const lines = data.split('\n').slice(0, 10);
                lines.forEach(line => console.log(`     ${line}`));
            }
            
            console.log('\n4. Testing plugin file:');
            const plugin_file = path.join('./plugins', 'dynamic_ip_updater.js');
            fs.access(plugin_file, fs.constants.F_OK, (err) => {
                if (err) {
                    console.error(`   ❌ Plugin file not found: ${plugin_file}`);
                } else {
                    console.log(`   ✅ Plugin file exists: ${plugin_file}`);
                }
                
                console.log('\n5. Testing plugins configuration:');
                const plugins_file = path.join(config_dir, 'plugins');
                fs.readFile(plugins_file, 'utf8', (err, data) => {
                    if (err) {
                        console.error(`   ❌ Could not read ${plugins_file}: ${err.message}`);
                    } else {
                        if (data.includes('dynamic_ip_updater')) {
                            console.log(`   ✅ Plugin is enabled in plugins configuration`);
                        } else {
                            console.log(`   ⚠️  Plugin is NOT enabled in plugins configuration`);
                        }
                    }
                    
                    console.log('\n✅ Test completed!');
                    console.log('\nTo start using the plugin:');
                    console.log('1. Restart Haraka to load the new plugin');
                    console.log('2. Check the logs for plugin initialization messages');
                    console.log('3. The plugin will automatically update the relay ACL when IP changes');
                });
            });
        });
    });
});
