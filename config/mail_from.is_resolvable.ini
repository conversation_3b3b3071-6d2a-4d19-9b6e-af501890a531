[main]
; Skip mail_from.is_resolvable checks for relaying connections
; This allows authenticated/relaying users to send emails without
; requiring their MAIL FROM domain to be resolvable
skip_relaying=true

; Action to take when domain is not resolvable
; Options: reject, tempfail, quarantine, tag, none
; Default: reject
action=reject

; Allow private/internal domains that may not be publicly resolvable
allow_private_domains=true
