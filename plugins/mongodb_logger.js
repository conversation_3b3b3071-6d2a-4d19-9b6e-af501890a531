// MongoDB Email Logger Plugin for Haraka
// Comprehensive email transaction logging to MongoDB

const { MongoClient } = require('mongodb');
const crypto = require('crypto');

exports.register = function () {
    const plugin = this;
    
    plugin.loginfo('MongoDB Logger plugin loading...');
    
    // Load configuration
    plugin.load_config();
    
    // Initialize MongoDB connection
    plugin.mongodb_client = null;
    plugin.mongodb_db = null;
    plugin.connection_ready = false;
    plugin.batch_queue = [];
    plugin.batch_timer = null;
    
    // Connect to MongoDB
    plugin.connect_mongodb();
    
    // Register hooks for comprehensive logging
    plugin.register_hook('connect', 'log_connection');
    plugin.register_hook('helo', 'log_helo');
    plugin.register_hook('ehlo', 'log_ehlo');
    plugin.register_hook('mail', 'log_mail_from');
    plugin.register_hook('rcpt', 'log_rcpt_to');
    plugin.register_hook('data', 'log_data_start');
    plugin.register_hook('data_post', 'log_data_complete');
    plugin.register_hook('queue', 'log_queue');
    plugin.register_hook('queue_outbound', 'log_outbound');
    plugin.register_hook('bounce', 'log_bounce');
    plugin.register_hook('delivered', 'log_delivered');
    plugin.register_hook('deferred', 'log_deferred');
    plugin.register_hook('deny', 'log_deny');
    plugin.register_hook('disconnect', 'log_disconnect');
    
    plugin.loginfo('MongoDB Logger plugin registered successfully');
}

exports.load_config = function () {
    const plugin = this;
    
    plugin.cfg = plugin.config.get('mongodb_logger.ini', {
        booleans: [
            '+main.enabled',
            '+main.log_all_transactions',
            '+main.log_headers',
            '+main.log_body',
            '+main.sanitize_headers',
            '+main.enable_indexing',
            '+privacy.strip_private_ips',
            '+privacy.strip_internal_headers',
            '+privacy.anonymize_local_addresses',
            '+logging.log_connection_details',
            '+logging.log_performance_metrics',
            '+logging.log_errors',
            '+indexing.create_indexes_on_startup',
            '+indexing.index_timestamp',
            '+indexing.index_transaction_id',
            '+indexing.index_sender',
            '+indexing.index_recipient',
            '+indexing.index_source_ip',
            '+indexing.index_status'
        ]
    }, function() {
        plugin.load_config();
    });
    
    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (plugin.cfg.main.host === undefined) plugin.cfg.main.host = 'localhost';
    if (plugin.cfg.main.port === undefined) plugin.cfg.main.port = 27017;
    if (plugin.cfg.main.database === undefined) plugin.cfg.main.database = 'haraka_email_logs';

    // Debug logging to see what configuration was loaded
    plugin.loginfo('MongoDB config loaded - host: ' + plugin.cfg.main.host + ', port: ' + plugin.cfg.main.port + ', database: ' + plugin.cfg.main.database);
    if (plugin.cfg.main.connection_timeout === undefined) plugin.cfg.main.connection_timeout = 5000;
    if (plugin.cfg.main.socket_timeout === undefined) plugin.cfg.main.socket_timeout = 30000;
    if (plugin.cfg.main.max_pool_size === undefined) plugin.cfg.main.max_pool_size = 10;
    if (plugin.cfg.main.log_all_transactions === undefined) plugin.cfg.main.log_all_transactions = true;
    if (plugin.cfg.main.log_headers === undefined) plugin.cfg.main.log_headers = true;
    if (plugin.cfg.main.log_body === undefined) plugin.cfg.main.log_body = false;
    if (plugin.cfg.main.sanitize_headers === undefined) plugin.cfg.main.sanitize_headers = true;
    if (plugin.cfg.main.retention_days === undefined) plugin.cfg.main.retention_days = 365;
    if (plugin.cfg.main.batch_size === undefined) plugin.cfg.main.batch_size = 100;
    if (plugin.cfg.main.batch_timeout === undefined) plugin.cfg.main.batch_timeout = 5000;
    
    if (!plugin.cfg.collections) plugin.cfg.collections = {};
    if (plugin.cfg.collections.transactions === undefined) plugin.cfg.collections.transactions = 'email_transactions';
    if (plugin.cfg.collections.connections === undefined) plugin.cfg.collections.connections = 'email_connections';
    if (plugin.cfg.collections.errors === undefined) plugin.cfg.collections.errors = 'email_errors';
    if (plugin.cfg.collections.stats === undefined) plugin.cfg.collections.stats = 'email_stats';
    
    if (!plugin.cfg.privacy) plugin.cfg.privacy = {};
    if (plugin.cfg.privacy.strip_private_ips === undefined) plugin.cfg.privacy.strip_private_ips = true;
    if (plugin.cfg.privacy.strip_internal_headers === undefined) plugin.cfg.privacy.strip_internal_headers = true;
    
    plugin.loginfo('MongoDB Logger configuration loaded');
}

exports.connect_mongodb = function () {
    const plugin = this;
    
    if (!plugin.cfg.main.enabled) {
        plugin.loginfo('MongoDB logging is disabled');
        return;
    }
    
    const connection_string = 'mongodb://' + 
        (plugin.cfg.main.username ? plugin.cfg.main.username + ':' + plugin.cfg.main.password + '@' : '') +
        plugin.cfg.main.host + ':' + plugin.cfg.main.port + '/' + plugin.cfg.main.database;
    
    const options = {
        connectTimeoutMS: plugin.cfg.main.connection_timeout,
        socketTimeoutMS: plugin.cfg.main.socket_timeout,
        serverSelectionTimeoutMS: plugin.cfg.main.connection_timeout,
        maxPoolSize: plugin.cfg.main.max_pool_size,
        retryWrites: true,
        w: 'majority',
        directConnection: true,  // For localhost connections
        family: 4  // Force IPv4
    };

    plugin.loginfo('Connecting to MongoDB: ' + plugin.cfg.main.host + ':' + plugin.cfg.main.port);
    plugin.logdebug('MongoDB connection string: ' + connection_string);
    plugin.logdebug('MongoDB connection options: ' + JSON.stringify(options));
    
    // Add retry logic for connection
    plugin.connect_with_retry(connection_string, options, 3);
}

exports.connect_with_retry = function (connection_string, options, retries) {
    const plugin = this;

    MongoClient.connect(connection_string, options)
        .then(client => {
            plugin.mongodb_client = client;
            plugin.mongodb_db = client.db(plugin.cfg.main.database);
            plugin.connection_ready = true;

            plugin.loginfo('Successfully connected to MongoDB database: ' + plugin.cfg.main.database);

            // Create indexes if enabled
            if (plugin.cfg.indexing.create_indexes_on_startup) {
                plugin.create_indexes();
            }

            // Start batch processing
            plugin.start_batch_processing();

        })
        .catch(err => {
            plugin.logerror('Failed to connect to MongoDB: ' + err.message);
            plugin.logerror('Error details - Code: ' + err.code + ', Name: ' + err.name);

            if (retries > 0) {
                plugin.loginfo('Retrying MongoDB connection in 5 seconds... (' + retries + ' retries left)');
                setTimeout(() => {
                    plugin.connect_with_retry(connection_string, options, retries - 1);
                }, 5000);
            } else {
                plugin.logerror('All MongoDB connection retries exhausted');
                plugin.connection_ready = false;
            }
        });
}

exports.create_indexes = function () {
    const plugin = this;

    if (!plugin.connection_ready) return;

    plugin.loginfo('Creating MongoDB indexes...');

    const transactions_collection = plugin.mongodb_db.collection(plugin.cfg.collections.transactions);
    const connections_collection = plugin.mongodb_db.collection(plugin.cfg.collections.connections);

    // Create indexes for transactions collection
    const transaction_indexes = [];

    if (plugin.cfg.indexing.index_timestamp) {
        transaction_indexes.push({ timestamp: -1 });
    }
    if (plugin.cfg.indexing.index_transaction_id) {
        transaction_indexes.push({ transaction_id: 1 });
    }
    if (plugin.cfg.indexing.index_sender) {
        transaction_indexes.push({ 'mail_from.address': 1 });
    }
    if (plugin.cfg.indexing.index_recipient) {
        transaction_indexes.push({ 'rcpt_to.address': 1 });
    }
    if (plugin.cfg.indexing.index_source_ip) {
        transaction_indexes.push({ 'connection.remote_ip': 1 });
    }
    if (plugin.cfg.indexing.index_status) {
        transaction_indexes.push({ status: 1 });
    }

    // Compound indexes for common queries
    transaction_indexes.push({ timestamp: -1, status: 1 });
    transaction_indexes.push({ 'connection.remote_ip': 1, timestamp: -1 });
    transaction_indexes.push({ 'mail_from.address': 1, timestamp: -1 });

    // Create indexes
    transaction_indexes.forEach(index => {
        transactions_collection.createIndex(index)
            .then(() => plugin.logdebug('Created index: ' + JSON.stringify(index)))
            .catch(err => plugin.logwarn('Failed to create index: ' + err.message));
    });

    // Create TTL index for retention
    if (plugin.cfg.main.retention_days > 0) {
        const ttl_seconds = plugin.cfg.main.retention_days * 24 * 60 * 60;
        transactions_collection.createIndex(
            { timestamp: 1 },
            { expireAfterSeconds: ttl_seconds }
        )
        .then(() => plugin.loginfo('Created TTL index with ' + plugin.cfg.main.retention_days + ' days retention'))
        .catch(err => plugin.logwarn('Failed to create TTL index: ' + err.message));
    }
}

exports.start_batch_processing = function () {
    const plugin = this;

    plugin.batch_timer = setInterval(() => {
        plugin.flush_batch();
    }, plugin.cfg.main.batch_timeout);

    plugin.logdebug('Started batch processing with ' + plugin.cfg.main.batch_timeout + 'ms timeout');
}

exports.flush_batch = function () {
    const plugin = this;

    if (!plugin.connection_ready || plugin.batch_queue.length === 0) {
        return;
    }

    const batch = plugin.batch_queue.splice(0, plugin.cfg.main.batch_size);

    if (batch.length === 0) return;

    const transactions_collection = plugin.mongodb_db.collection(plugin.cfg.collections.transactions);

    transactions_collection.insertMany(batch)
        .then(result => {
            if (plugin.cfg.logging.log_performance_metrics) {
                plugin.logdebug('Inserted ' + result.insertedCount + ' documents to MongoDB');
            }
        })
        .catch(err => {
            plugin.logerror('Failed to insert batch to MongoDB: ' + err.message);
            // Re-queue failed items (optional)
            // plugin.batch_queue.unshift(...batch);
        });
}

// Utility functions
exports.get_transaction_data = function (connection) {
    const plugin = this;
    const txn = connection.transaction;

    if (!connection._mongodb_log_data) {
        connection._mongodb_log_data = {
            transaction_id: txn ? txn.uuid : plugin.generate_uuid(),
            timestamp: new Date(),
            connection: {
                remote_ip: connection.remote?.ip,
                remote_host: connection.remote?.host,
                local_ip: connection.local?.ip,
                local_port: connection.local?.port,
                tls: connection.tls ? {
                    enabled: true,
                    cipher: connection.tls.cipher,
                    version: connection.tls.version
                } : { enabled: false }
            },
            helo: null,
            mail_from: null,
            rcpt_to: [],
            headers: {},
            message: {
                size: 0,
                subject: null,
                message_id: null
            },
            authentication: {
                spf: null,
                dkim: null,
                dmarc: null
            },
            status: 'in_progress',
            processing_time: 0,
            errors: [],
            timestamps: {
                connect: new Date(),
                helo: null,
                mail_from: null,
                data_start: null,
                data_complete: null,
                queue: null,
                disconnect: null
            }
        };
    }

    return connection._mongodb_log_data;
}

exports.sanitize_headers = function (headers) {
    const plugin = this;

    if (!plugin.cfg.main.sanitize_headers) {
        return headers;
    }

    const sanitized = {};
    const exclude_headers = plugin.cfg.privacy.exclude_headers ?
        plugin.cfg.privacy.exclude_headers.split(',').map(h => h.trim().toLowerCase()) : [];

    for (const name in headers) {
        const name_lower = name.toLowerCase();

        // Skip excluded headers
        if (exclude_headers.includes(name_lower)) {
            continue;
        }

        let value = headers[name];

        // Strip private IPs if enabled
        if (plugin.cfg.privacy.strip_private_ips && typeof value === 'string') {
            // Remove common private IP patterns
            value = value.replace(/\b192\.168\.\d+\.\d+\b/g, '[PRIVATE_IP]');
            value = value.replace(/\b10\.\d+\.\d+\.\d+\b/g, '[PRIVATE_IP]');
            value = value.replace(/\b172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+\b/g, '[PRIVATE_IP]');

            // Remove our dynamic IP if we can detect it
            // This integrates with our existing privacy protection
            if (connection._home_server_ip && value.includes(connection._home_server_ip)) {
                value = value.replace(new RegExp(connection._home_server_ip.replace(/\./g, '\\.'), 'g'), '[HOME_SERVER_IP]');
            }
        }

        sanitized[name] = value;
    }

    return sanitized;
}

exports.generate_uuid = function () {
    return crypto.randomUUID();
}

exports.queue_log_entry = function (log_data) {
    const plugin = this;

    if (!plugin.cfg.main.enabled || !plugin.connection_ready) {
        return;
    }

    plugin.batch_queue.push(log_data);

    // Flush immediately if batch is full
    if (plugin.batch_queue.length >= plugin.cfg.main.batch_size) {
        plugin.flush_batch();
    }
}

// Hook functions for email transaction logging
exports.log_connection = function (next, connection) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.timestamps.connect = new Date();

    if (plugin.cfg.logging.log_connection_details) {
        plugin.logdebug('New connection from: ' + connection.remote?.ip);
    }

    next();
}

exports.log_helo = function (next, connection, helo) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.helo = helo;
    log_data.timestamps.helo = new Date();

    next();
}

exports.log_ehlo = function (next, connection, helo) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.helo = helo;
    log_data.timestamps.helo = new Date();

    next();
}

exports.log_mail_from = function (next, connection, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.mail_from = {
        address: params[0] ? params[0].address() : null,
        original: params[0] ? params[0].original : null,
        host: params[0] ? params[0].host : null
    };
    log_data.timestamps.mail_from = new Date();

    next();
}

exports.log_rcpt_to = function (next, connection, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.rcpt_to.push({
        address: params[0] ? params[0].address() : null,
        original: params[0] ? params[0].original : null,
        host: params[0] ? params[0].host : null,
        timestamp: new Date()
    });

    next();
}

exports.log_data_start = function (next, connection) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.timestamps.data_start = new Date();

    next();
}

exports.log_data_complete = function (next, connection) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const txn = connection.transaction;
    if (!txn) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.timestamps.data_complete = new Date();

    // Capture message details
    log_data.message.size = txn.data_bytes || 0;

    // Extract headers if enabled
    if (plugin.cfg.main.log_headers && txn.header) {
        const raw_headers = {};

        // Get headers using Haraka's header methods
        if (txn.header.headers) {
            for (const name in txn.header.headers) {
                raw_headers[name] = txn.header.headers[name];
            }
        }

        // Extract important headers
        log_data.message.subject = txn.header.get('subject');
        log_data.message.message_id = txn.header.get('message-id');

        // Sanitize headers for privacy
        log_data.headers = plugin.sanitize_headers(raw_headers);
    }

    // Capture authentication results
    if (connection.notes) {
        if (connection.notes.spf_mail_result) {
            log_data.authentication.spf = {
                result: connection.notes.spf_mail_result,
                domain: connection.notes.spf_mail_domain
            };
        }

        if (connection.notes.dkim_results) {
            log_data.authentication.dkim = connection.notes.dkim_results;
        }

        if (connection.notes.dmarc_result) {
            log_data.authentication.dmarc = connection.notes.dmarc_result;
        }
    }

    next();
}

exports.log_queue = function (next, connection, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.timestamps.queue = new Date();
    log_data.status = 'queued';

    // Calculate processing time
    if (log_data.timestamps.connect) {
        log_data.processing_time = Date.now() - log_data.timestamps.connect.getTime();
    }

    // Queue for MongoDB insertion
    plugin.queue_log_entry(Object.assign({}, log_data));

    next();
}

exports.log_outbound = function (next, connection, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.status = 'relayed';
    log_data.timestamps.queue = new Date();

    // Calculate processing time
    if (log_data.timestamps.connect) {
        log_data.processing_time = Date.now() - log_data.timestamps.connect.getTime();
    }

    // Queue for MongoDB insertion
    plugin.queue_log_entry(Object.assign({}, log_data));

    next();
}

exports.log_bounce = function (next, hmail, error) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    // Log bounce information
    const bounce_data = {
        transaction_id: hmail.todo ? hmail.todo.uuid : plugin.generate_uuid(),
        timestamp: new Date(),
        status: 'bounced',
        bounce_reason: error ? error.message : 'Unknown bounce reason',
        recipient: hmail.todo ? hmail.todo.rcpt_to : null,
        sender: hmail.todo ? hmail.todo.mail_from : null,
        processing_time: 0
    };

    plugin.queue_log_entry(bounce_data);

    next();
}

exports.log_delivered = function (next, hmail, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    // Log successful delivery
    const delivery_data = {
        transaction_id: hmail.todo ? hmail.todo.uuid : plugin.generate_uuid(),
        timestamp: new Date(),
        status: 'delivered',
        recipient: hmail.todo ? hmail.todo.rcpt_to : null,
        sender: hmail.todo ? hmail.todo.mail_from : null,
        delivery_details: params || {},
        processing_time: 0
    };

    plugin.queue_log_entry(delivery_data);

    next();
}

exports.log_deferred = function (next, hmail, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    // Log deferred delivery
    const defer_data = {
        transaction_id: hmail.todo ? hmail.todo.uuid : plugin.generate_uuid(),
        timestamp: new Date(),
        status: 'deferred',
        defer_reason: params ? params.toString() : 'Unknown defer reason',
        recipient: hmail.todo ? hmail.todo.rcpt_to : null,
        sender: hmail.todo ? hmail.todo.mail_from : null,
        processing_time: 0
    };

    plugin.queue_log_entry(defer_data);

    next();
}

exports.log_deny = function (next, connection, params) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.status = 'rejected';
    log_data.rejection_reason = params ? params.toString() : 'Unknown rejection reason';
    log_data.timestamps.disconnect = new Date();

    // Calculate processing time
    if (log_data.timestamps.connect) {
        log_data.processing_time = Date.now() - log_data.timestamps.connect.getTime();
    }

    // Queue for MongoDB insertion
    plugin.queue_log_entry(Object.assign({}, log_data));

    next();
}

exports.log_disconnect = function (next, connection) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const log_data = plugin.get_transaction_data(connection);
    log_data.timestamps.disconnect = new Date();

    // Only log if we haven't already logged this transaction
    if (log_data.status === 'in_progress') {
        log_data.status = 'disconnected';

        // Calculate processing time
        if (log_data.timestamps.connect) {
            log_data.processing_time = Date.now() - log_data.timestamps.connect.getTime();
        }

        // Queue for MongoDB insertion
        plugin.queue_log_entry(Object.assign({}, log_data));
    }

    next();
}

exports.shutdown = function () {
    const plugin = this;

    plugin.loginfo('MongoDB Logger plugin shutting down...');

    // Flush any remaining batch items
    plugin.flush_batch();

    // Clear batch timer
    if (plugin.batch_timer) {
        clearInterval(plugin.batch_timer);
        plugin.batch_timer = null;
    }

    // Close MongoDB connection
    if (plugin.mongodb_client) {
        plugin.mongodb_client.close()
            .then(() => plugin.loginfo('MongoDB connection closed'))
            .catch(err => plugin.logerror('Error closing MongoDB connection: ' + err.message));
    }
}
