{"ast": null, "code": "import React,{useState}from'react';import{Link,useLocation}from'react-router-dom';import{Mail,BarChart3,List,Menu,X,Home,Activity,Server,LogOut,User}from'lucide-react';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);const location=useLocation();const{user,logout}=useAuth();const handleLogout=async()=>{await logout();};const navigation=[{name:'Dashboard',href:'/',icon:Home},{name:'Transactions',href:'/transactions',icon:List},{name:'Statistics',href:'/statistics',icon:BarChart3}];const isActive=href=>{if(href==='/'){return location.pathname==='/';}return location.pathname.startsWith(href);};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsxs(\"div\",{className:`fixed inset-0 z-40 lg:hidden ${sidebarOpen?'block':'hidden'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-75\",onClick:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex w-full max-w-xs flex-1 flex-col bg-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-0 right-0 -mr-12 pt-2\",children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",onClick:()=>setSidebarOpen(false),children:/*#__PURE__*/_jsx(X,{className:\"h-6 w-6 text-white\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"h-0 flex-1 overflow-y-auto pt-5 pb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-shrink-0 items-center px-4\",children:[/*#__PURE__*/_jsx(Mail,{className:\"h-8 w-8 text-primary-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-xl font-bold text-gray-900\",children:\"Haraka Dashboard\"})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-5 space-y-1 px-2\",children:navigation.map(item=>{const Icon=item.icon;return/*#__PURE__*/_jsxs(Link,{to:item.href,className:`group flex items-center px-2 py-2 text-base font-medium rounded-md ${isActive(item.href)?'bg-primary-100 text-primary-900':'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,onClick:()=>setSidebarOpen(false),children:[/*#__PURE__*/_jsx(Icon,{className:\"mr-4 h-6 w-6 flex-shrink-0\"}),item.name]},item.name);})})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-1 flex-col overflow-y-auto pt-5 pb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-shrink-0 items-center px-4\",children:[/*#__PURE__*/_jsx(Mail,{className:\"h-8 w-8 text-primary-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-xl font-bold text-gray-900\",children:\"Haraka Dashboard\"})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-5 flex-1 space-y-1 px-2\",children:navigation.map(item=>{const Icon=item.icon;return/*#__PURE__*/_jsxs(Link,{to:item.href,className:`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${isActive(item.href)?'bg-primary-100 text-primary-900':'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,children:[/*#__PURE__*/_jsx(Icon,{className:\"mr-3 h-6 w-6 flex-shrink-0\"}),item.name]},item.name);})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-shrink-0 border-t border-gray-200 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(User,{className:\"h-8 w-8 text-gray-400\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-700\",children:user===null||user===void 0?void 0:user.username}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 capitalize\",children:user===null||user===void 0?void 0:user.role})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleLogout,className:\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md\",title:\"Logout\",children:/*#__PURE__*/_jsx(LogOut,{className:\"h-5 w-5\"})})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:pl-64\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"sticky top-0 z-10 bg-white border-b border-gray-200 lg:hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-16 items-center justify-between px-4\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"border-r border-gray-200 px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden\",onClick:()=>setSidebarOpen(true),children:/*#__PURE__*/_jsx(Menu,{className:\"h-6 w-6\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(Mail,{className:\"h-8 w-8 text-primary-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-lg font-bold text-gray-900\",children:\"Haraka Dashboard\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:user===null||user===void 0?void 0:user.username}),/*#__PURE__*/_jsx(\"button\",{onClick:handleLogout,className:\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md\",title:\"Logout\",children:/*#__PURE__*/_jsx(LogOut,{className:\"h-5 w-5\"})})]})]})}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",children:children})})})]})]});};export default Layout;", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "Mail", "BarChart3", "List", "<PERSON><PERSON>", "X", "Home", "Activity", "Server", "LogOut", "User", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Layout", "_ref", "children", "sidebarOpen", "setSidebarOpen", "location", "user", "logout", "handleLogout", "navigation", "name", "href", "icon", "isActive", "pathname", "startsWith", "className", "onClick", "type", "map", "item", "Icon", "to", "username", "role", "title"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Mail,\n  BarChart3,\n  List,\n  Menu,\n  X,\n  Home,\n  Activity,\n  Server,\n  LogOut,\n  User\n} from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Layout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const location = useLocation();\n  const { user, logout } = useAuth();\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/', icon: Home },\n    { name: 'Transactions', href: '/transactions', icon: List },\n    { name: 'Statistics', href: '/statistics', icon: BarChart3 },\n  ];\n\n  const isActive = (href) => {\n    if (href === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(href);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex w-full max-w-xs flex-1 flex-col bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <div className=\"h-0 flex-1 overflow-y-auto pt-5 pb-4\">\n            <div className=\"flex flex-shrink-0 items-center px-4\">\n              <Mail className=\"h-8 w-8 text-primary-600\" />\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">Haraka Dashboard</span>\n            </div>\n            <nav className=\"mt-5 space-y-1 px-2\">\n              {navigation.map((item) => {\n                const Icon = item.icon;\n                return (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${\n                      isActive(item.href)\n                        ? 'bg-primary-100 text-primary-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <Icon className=\"mr-4 h-6 w-6 flex-shrink-0\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex flex-1 flex-col overflow-y-auto pt-5 pb-4\">\n            <div className=\"flex flex-shrink-0 items-center px-4\">\n              <Mail className=\"h-8 w-8 text-primary-600\" />\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">Haraka Dashboard</span>\n            </div>\n            <nav className=\"mt-5 flex-1 space-y-1 px-2\">\n              {navigation.map((item) => {\n                const Icon = item.icon;\n                return (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                      isActive(item.href)\n                        ? 'bg-primary-100 text-primary-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <Icon className=\"mr-3 h-6 w-6 flex-shrink-0\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n          <div className=\"flex flex-shrink-0 border-t border-gray-200 p-4\">\n            <div className=\"flex items-center justify-between w-full\">\n              <div className=\"flex items-center\">\n                <User className=\"h-8 w-8 text-gray-400\" />\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-700\">{user?.username}</p>\n                  <p className=\"text-xs text-gray-500 capitalize\">{user?.role}</p>\n                </div>\n              </div>\n              <button\n                onClick={handleLogout}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md\"\n                title=\"Logout\"\n              >\n                <LogOut className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-10 bg-white border-b border-gray-200 lg:hidden\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <button\n              type=\"button\"\n              className=\"border-r border-gray-200 px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n            <div className=\"flex items-center\">\n              <Mail className=\"h-8 w-8 text-primary-600\" />\n              <span className=\"ml-2 text-lg font-bold text-gray-900\">Haraka Dashboard</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">{user?.username}</span>\n              <button\n                onClick={handleLogout}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md\"\n                title=\"Logout\"\n              >\n                <LogOut className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OACEC,IAAI,CACJC,SAAS,CACTC,IAAI,CACJC,IAAI,CACJC,CAAC,CACDC,IAAI,CACJC,QAAQ,CACRC,MAAM,CACNC,MAAM,CACNC,IAAI,KACC,cAAc,CACrB,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC1B,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAAuB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEsB,IAAI,CAAEC,MAAO,CAAC,CAAGZ,OAAO,CAAC,CAAC,CAElC,KAAM,CAAAa,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAD,MAAM,CAAC,CAAC,CAChB,CAAC,CAED,KAAM,CAAAE,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,GAAG,CAAEC,IAAI,CAAEtB,IAAK,CAAC,CAC5C,CAAEoB,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAE,eAAe,CAAEC,IAAI,CAAEzB,IAAK,CAAC,CAC3D,CAAEuB,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAE1B,SAAU,CAAC,CAC7D,CAED,KAAM,CAAA2B,QAAQ,CAAIF,IAAI,EAAK,CACzB,GAAIA,IAAI,GAAK,GAAG,CAAE,CAChB,MAAO,CAAAN,QAAQ,CAACS,QAAQ,GAAK,GAAG,CAClC,CACA,MAAO,CAAAT,QAAQ,CAACS,QAAQ,CAACC,UAAU,CAACJ,IAAI,CAAC,CAC3C,CAAC,CAED,mBACEZ,KAAA,QAAKiB,SAAS,CAAC,yBAAyB,CAAAd,QAAA,eAEtCH,KAAA,QAAKiB,SAAS,CAAE,gCAAgCb,WAAW,CAAG,OAAO,CAAG,QAAQ,EAAG,CAAAD,QAAA,eACjFL,IAAA,QAAKmB,SAAS,CAAC,yCAAyC,CAACC,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,KAAK,CAAE,CAAE,CAAC,cACjGL,KAAA,QAAKiB,SAAS,CAAC,wDAAwD,CAAAd,QAAA,eACrEL,IAAA,QAAKmB,SAAS,CAAC,oCAAoC,CAAAd,QAAA,cACjDL,IAAA,WACEqB,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,gIAAgI,CAC1IC,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,cAErCL,IAAA,CAACR,CAAC,EAAC2B,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC9B,CAAC,CACN,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,sCAAsC,CAAAd,QAAA,eACnDH,KAAA,QAAKiB,SAAS,CAAC,sCAAsC,CAAAd,QAAA,eACnDL,IAAA,CAACZ,IAAI,EAAC+B,SAAS,CAAC,0BAA0B,CAAE,CAAC,cAC7CnB,IAAA,SAAMmB,SAAS,CAAC,sCAAsC,CAAAd,QAAA,CAAC,kBAAgB,CAAM,CAAC,EAC3E,CAAC,cACNL,IAAA,QAAKmB,SAAS,CAAC,qBAAqB,CAAAd,QAAA,CACjCO,UAAU,CAACU,GAAG,CAAEC,IAAI,EAAK,CACxB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACR,IAAI,CACtB,mBACEb,KAAA,CAAChB,IAAI,EAEHuC,EAAE,CAAEF,IAAI,CAACT,IAAK,CACdK,SAAS,CAAE,sEACTH,QAAQ,CAACO,IAAI,CAACT,IAAI,CAAC,CACf,iCAAiC,CACjC,oDAAoD,EACvD,CACHM,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,eAErCL,IAAA,CAACwB,IAAI,EAACL,SAAS,CAAC,4BAA4B,CAAE,CAAC,CAC9CI,IAAI,CAACV,IAAI,GAVLU,IAAI,CAACV,IAWN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNb,IAAA,QAAKmB,SAAS,CAAC,0DAA0D,CAAAd,QAAA,cACvEH,KAAA,QAAKiB,SAAS,CAAC,gEAAgE,CAAAd,QAAA,eAC7EH,KAAA,QAAKiB,SAAS,CAAC,gDAAgD,CAAAd,QAAA,eAC7DH,KAAA,QAAKiB,SAAS,CAAC,sCAAsC,CAAAd,QAAA,eACnDL,IAAA,CAACZ,IAAI,EAAC+B,SAAS,CAAC,0BAA0B,CAAE,CAAC,cAC7CnB,IAAA,SAAMmB,SAAS,CAAC,sCAAsC,CAAAd,QAAA,CAAC,kBAAgB,CAAM,CAAC,EAC3E,CAAC,cACNL,IAAA,QAAKmB,SAAS,CAAC,4BAA4B,CAAAd,QAAA,CACxCO,UAAU,CAACU,GAAG,CAAEC,IAAI,EAAK,CACxB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACR,IAAI,CACtB,mBACEb,KAAA,CAAChB,IAAI,EAEHuC,EAAE,CAAEF,IAAI,CAACT,IAAK,CACdK,SAAS,CAAE,oEACTH,QAAQ,CAACO,IAAI,CAACT,IAAI,CAAC,CACf,iCAAiC,CACjC,oDAAoD,EACvD,CAAAT,QAAA,eAEHL,IAAA,CAACwB,IAAI,EAACL,SAAS,CAAC,4BAA4B,CAAE,CAAC,CAC9CI,IAAI,CAACV,IAAI,GATLU,IAAI,CAACV,IAUN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cACNb,IAAA,QAAKmB,SAAS,CAAC,iDAAiD,CAAAd,QAAA,cAC9DH,KAAA,QAAKiB,SAAS,CAAC,0CAA0C,CAAAd,QAAA,eACvDH,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAd,QAAA,eAChCL,IAAA,CAACH,IAAI,EAACsB,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC1CjB,KAAA,QAAKiB,SAAS,CAAC,MAAM,CAAAd,QAAA,eACnBL,IAAA,MAAGmB,SAAS,CAAC,mCAAmC,CAAAd,QAAA,CAAEI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,QAAQ,CAAI,CAAC,cACrE1B,IAAA,MAAGmB,SAAS,CAAC,kCAAkC,CAAAd,QAAA,CAAEI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEkB,IAAI,CAAI,CAAC,EAC7D,CAAC,EACH,CAAC,cACN3B,IAAA,WACEoB,OAAO,CAAET,YAAa,CACtBQ,SAAS,CAAC,oEAAoE,CAC9ES,KAAK,CAAC,QAAQ,CAAAvB,QAAA,cAEdL,IAAA,CAACJ,MAAM,EAACuB,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNjB,KAAA,QAAKiB,SAAS,CAAC,UAAU,CAAAd,QAAA,eAEvBL,IAAA,QAAKmB,SAAS,CAAC,+DAA+D,CAAAd,QAAA,cAC5EH,KAAA,QAAKiB,SAAS,CAAC,6CAA6C,CAAAd,QAAA,eAC1DL,IAAA,WACEqB,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,+HAA+H,CACzIC,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,IAAI,CAAE,CAAAF,QAAA,cAEpCL,IAAA,CAACT,IAAI,EAAC4B,SAAS,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cACTjB,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAd,QAAA,eAChCL,IAAA,CAACZ,IAAI,EAAC+B,SAAS,CAAC,0BAA0B,CAAE,CAAC,cAC7CnB,IAAA,SAAMmB,SAAS,CAAC,sCAAsC,CAAAd,QAAA,CAAC,kBAAgB,CAAM,CAAC,EAC3E,CAAC,cACNH,KAAA,QAAKiB,SAAS,CAAC,6BAA6B,CAAAd,QAAA,eAC1CL,IAAA,SAAMmB,SAAS,CAAC,uBAAuB,CAAAd,QAAA,CAAEI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,QAAQ,CAAO,CAAC,cAC/D1B,IAAA,WACEoB,OAAO,CAAET,YAAa,CACtBQ,SAAS,CAAC,oEAAoE,CAC9ES,KAAK,CAAC,QAAQ,CAAAvB,QAAA,cAEdL,IAAA,CAACJ,MAAM,EAACuB,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGNnB,IAAA,SAAMmB,SAAS,CAAC,QAAQ,CAAAd,QAAA,cACtBL,IAAA,QAAKmB,SAAS,CAAC,MAAM,CAAAd,QAAA,cACnBL,IAAA,QAAKmB,SAAS,CAAC,wCAAwC,CAAAd,QAAA,CACpDA,QAAQ,CACN,CAAC,CACH,CAAC,CACF,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}