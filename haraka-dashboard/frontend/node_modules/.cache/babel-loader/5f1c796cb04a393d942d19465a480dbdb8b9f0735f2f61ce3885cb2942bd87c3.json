{"ast": null, "code": "import React from'react';import{Navigate}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,requireAdmin=false}=_ref;const{isAuthenticated,isAdmin,loading}=useAuth();if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner mx-auto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-sm text-gray-500\",children:\"Loading...\"})]})});}if(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}if(requireAdmin&&!isAdmin){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Access Denied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"You need admin privileges to access this page.\"})]})});}return children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "requireAdmin", "isAuthenticated", "isAdmin", "loading", "className", "to", "replace"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst ProtectedRoute = ({ children, requireAdmin = false }) => {\n  const { isAuthenticated, isAdmin, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"loading-spinner mx-auto\"></div>\n          <p className=\"mt-4 text-sm text-gray-500\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (requireAdmin && !isAdmin) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Access Denied</h2>\n          <p className=\"mt-2 text-gray-600\">You need admin privileges to access this page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAwC,IAAvC,CAAEC,QAAQ,CAAEC,YAAY,CAAG,KAAM,CAAC,CAAAF,IAAA,CACxD,KAAM,CAAEG,eAAe,CAAEC,OAAO,CAAEC,OAAQ,CAAC,CAAGX,OAAO,CAAC,CAAC,CAEvD,GAAIW,OAAO,CAAE,CACX,mBACET,IAAA,QAAKU,SAAS,CAAC,0DAA0D,CAAAL,QAAA,cACvEH,KAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAL,QAAA,eAC1BL,IAAA,QAAKU,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/CV,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAAL,QAAA,CAAC,YAAU,CAAG,CAAC,EACrD,CAAC,CACH,CAAC,CAEV,CAEA,GAAI,CAACE,eAAe,CAAE,CACpB,mBAAOP,IAAA,CAACH,QAAQ,EAACc,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA,GAAIN,YAAY,EAAI,CAACE,OAAO,CAAE,CAC5B,mBACER,IAAA,QAAKU,SAAS,CAAC,0DAA0D,CAAAL,QAAA,cACvEH,KAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAL,QAAA,eAC1BL,IAAA,OAAIU,SAAS,CAAC,kCAAkC,CAAAL,QAAA,CAAC,eAAa,CAAI,CAAC,cACnEL,IAAA,MAAGU,SAAS,CAAC,oBAAoB,CAAAL,QAAA,CAAC,gDAA8C,CAAG,CAAC,EACjF,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}