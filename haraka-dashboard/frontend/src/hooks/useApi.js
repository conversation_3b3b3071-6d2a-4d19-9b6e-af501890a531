import { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Custom hook for API calls
export const useApi = (url, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated, loading: authLoading } = useAuth();

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get(url, options);
      setData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch data if user is authenticated and auth is not loading
    if (!authLoading && isAuthenticated) {
      fetchData();
    } else if (!authLoading && !isAuthenticated) {
      // Clear data if not authenticated
      setData(null);
      setLoading(false);
      setError(null);
    }
  }, [url, isAuthenticated, authLoading]);

  const refetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch };
};

// Hook for transactions with pagination and filtering
export const useTransactions = (params = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated, loading: authLoading } = useAuth();

  const fetchTransactions = async (newParams = {}) => {
    try {
      setLoading(true);
      setError(null);
      
      const queryParams = { ...params, ...newParams };
      const queryString = new URLSearchParams(queryParams).toString();
      const url = `/transactions${queryString ? `?${queryString}` : ''}`;
      
      const response = await api.get(url);
      setData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch transactions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch data if user is authenticated and auth is not loading
    if (!authLoading && isAuthenticated) {
      fetchTransactions();
    } else if (!authLoading && !isAuthenticated) {
      // Clear data if not authenticated
      setData(null);
      setLoading(false);
      setError(null);
    }
  }, [isAuthenticated, authLoading]);

  const refetch = (newParams = {}) => {
    fetchTransactions(newParams);
  };

  return { data, loading, error, refetch };
};

// Hook for statistics
export const useStats = (timeframe = '24h') => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated, loading: authLoading } = useAuth();

  const fetchStats = async (newTimeframe = timeframe) => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get(`/stats?timeframe=${newTimeframe}`);
      setData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch data if user is authenticated and auth is not loading
    if (!authLoading && isAuthenticated) {
      fetchStats();
    } else if (!authLoading && !isAuthenticated) {
      // Clear data if not authenticated
      setData(null);
      setLoading(false);
      setError(null);
    }
  }, [timeframe, isAuthenticated, authLoading]);

  const refetch = (newTimeframe = timeframe) => {
    fetchStats(newTimeframe);
  };

  return { data, loading, error, refetch };
};

// Hook for single transaction
export const useTransaction = (id) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated, loading: authLoading } = useAuth();

  useEffect(() => {
    if (!id) return;

    const fetchTransaction = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await api.get(`/transactions/${id}`);
        setData(response.data);
      } catch (err) {
        setError(err.response?.data?.message || err.message || 'Failed to fetch transaction');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if user is authenticated and auth is not loading
    if (!authLoading && isAuthenticated) {
      fetchTransaction();
    } else if (!authLoading && !isAuthenticated) {
      // Clear data if not authenticated
      setData(null);
      setLoading(false);
      setError(null);
    }
  }, [id, isAuthenticated, authLoading]);

  return { data, loading, error };
};

export default api;
