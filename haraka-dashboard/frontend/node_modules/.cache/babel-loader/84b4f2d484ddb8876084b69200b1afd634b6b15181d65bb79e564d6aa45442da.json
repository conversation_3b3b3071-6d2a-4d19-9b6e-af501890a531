{"ast": null, "code": "import React from'react';import{<PERSON>rowserRouter as Router,Routes,Route}from'react-router-dom';import{AuthProvider}from'./contexts/AuthContext';import ProtectedRoute from'./components/ProtectedRoute';import Layout from'./components/Layout';import Login from'./pages/Login';import Dashboard from'./pages/Dashboard';import Transactions from'./pages/Transactions';import TransactionDetail from'./pages/TransactionDetail';import Statistics from'./pages/Statistics';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(<PERSON><PERSON>,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Dashboard,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/transactions\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Transactions,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/transactions/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(TransactionDetail,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/statistics\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Statistics,{})})})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "Layout", "<PERSON><PERSON>", "Dashboard", "Transactions", "TransactionDetail", "Statistics", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport Transactions from './pages/Transactions';\nimport TransactionDetail from './pages/TransactionDetail';\nimport Statistics from './pages/Statistics';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/\" element={\n            <ProtectedRoute>\n              <Layout>\n                <Dashboard />\n              </Layout>\n            </ProtectedRoute>\n          } />\n          <Route path=\"/transactions\" element={\n            <ProtectedRoute>\n              <Layout>\n                <Transactions />\n              </Layout>\n            </ProtectedRoute>\n          } />\n          <Route path=\"/transactions/:id\" element={\n            <ProtectedRoute>\n              <Layout>\n                <TransactionDetail />\n              </Layout>\n            </ProtectedRoute>\n          } />\n          <Route path=\"/statistics\" element={\n            <ProtectedRoute>\n              <Layout>\n                <Statistics />\n              </Layout>\n            </ProtectedRoute>\n          } />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,OAASC,YAAY,KAAQ,wBAAwB,CACrD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACT,YAAY,EAAAa,QAAA,cACXJ,IAAA,CAACZ,MAAM,EAAAgB,QAAA,cACLF,KAAA,CAACb,MAAM,EAAAe,QAAA,eACLJ,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACN,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CM,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cACrBN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACP,MAAM,EAAAW,QAAA,cACLJ,IAAA,CAACL,SAAS,GAAE,CAAC,CACP,CAAC,CACK,CACjB,CAAE,CAAC,cACJK,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,eAAe,CAACC,OAAO,cACjCN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACP,MAAM,EAAAW,QAAA,cACLJ,IAAA,CAACJ,YAAY,GAAE,CAAC,CACV,CAAC,CACK,CACjB,CAAE,CAAC,cACJI,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrCN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACP,MAAM,EAAAW,QAAA,cACLJ,IAAA,CAACH,iBAAiB,GAAE,CAAC,CACf,CAAC,CACK,CACjB,CAAE,CAAC,cACJG,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,aAAa,CAACC,OAAO,cAC/BN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACP,MAAM,EAAAW,QAAA,cACLJ,IAAA,CAACF,UAAU,GAAE,CAAC,CACR,CAAC,CACK,CACjB,CAAE,CAAC,EACE,CAAC,CACH,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}