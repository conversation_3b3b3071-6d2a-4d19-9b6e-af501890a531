[main]
; SPF validation settings

; Action to take when SPF check fails
; Options: reject, tempfail, quarantine, tag
; Default: reject
action=reject

; Action to take when SPF check results in softfail (~all)
; Options: reject, tempfail, quarantine, tag, none
; Default: none
softfail_action=tag

; Action to take when no SPF record is found
; Options: reject, tempfail, quarantine, tag, none
; Default: none
no_record_action=none

; Action to take when SPF record has syntax errors
; Options: reject, tempfail, quarantine, tag, none
; Default: none
permerror_action=tag

; Action to take when temporary DNS errors occur
; Options: reject, tempfail, quarantine, tag, none
; Default: tempfail
temperror_action=tempfail

; Enable SPF checking for authenticated/relaying users
; Default: false (skip SPF for authenticated users)
; Set to false to skip SPF checks for relaying connections
check_relaying=false

; Log SPF results
; Default: true
log_result=true
