import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Mail, 
  Clock, 
  Users, 
  Globe,
  RefreshCw,
  Calendar
} from 'lucide-react';
import { useStats } from '../hooks/useApi';
import { formatDate, formatRelativeTime, getStatusBadge, formatEmail } from '../utils/formatters';

const Statistics = () => {
  const [timeframe, setTimeframe] = useState('24h');
  const { data: stats, loading, error, refetch } = useStats(timeframe);

  const timeframeOptions = [
    { value: '1h', label: 'Last Hour' },
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' }
  ];

  const handleTimeframeChange = (newTimeframe) => {
    setTimeframe(newTimeframe);
    refetch(newTimeframe);
  };

  const StatCard = ({ title, value, icon: Icon, color, subtitle }) => (
    <div className="card p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">
            {loading ? '...' : value}
          </p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );

  const TopListCard = ({ title, items, icon: Icon, emptyMessage }) => (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Icon className="h-5 w-5 text-gray-400 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
      </div>
      {loading ? (
        <div className="text-center py-4">
          <div className="loading-spinner mx-auto"></div>
        </div>
      ) : items && items.length > 0 ? (
        <div className="space-y-3">
          {items.slice(0, 10).map((item, index) => (
            <div key={item._id} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">
                  {index + 1}
                </span>
                <span className="text-sm text-gray-900 truncate max-w-xs">
                  {formatEmail(item._id)}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500">
                  {item.count}
                </span>
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary-600 h-2 rounded-full" 
                    style={{ 
                      width: `${Math.min(100, (item.count / Math.max(...items.map(i => i.count))) * 100)}%` 
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500 text-center py-4">{emptyMessage}</p>
      )}
    </div>
  );

  if (error) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading statistics</h3>
        <p className="mt-1 text-sm text-gray-500">{error}</p>
        <button
          onClick={() => refetch(timeframe)}
          className="mt-4 btn-primary"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Email Statistics
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Analyze your email server performance and trends
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <select
            value={timeframe}
            onChange={(e) => handleTimeframeChange(e.target.value)}
            className="select"
          >
            {timeframeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button
            onClick={() => refetch(timeframe)}
            className="btn-outline"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Transactions"
          value={stats?.totalTransactions?.toLocaleString() || '0'}
          icon={Mail}
          color="bg-primary-500"
          subtitle="All time"
        />
        <StatCard
          title={`Recent (${timeframeOptions.find(t => t.value === timeframe)?.label})`}
          value={stats?.recentTransactions?.toLocaleString() || '0'}
          icon={TrendingUp}
          color="bg-green-500"
          subtitle="New transactions"
        />
        <StatCard
          title="Success Rate"
          value={
            stats?.recentTransactions > 0 ? 
              `${Math.round(((stats?.statusStats?.find(s => s._id === 'delivered')?.count || 0) / stats.recentTransactions) * 100)}%` : 
              '0%'
          }
          icon={BarChart3}
          color="bg-blue-500"
          subtitle="Delivery success"
        />
        <StatCard
          title="Active Period"
          value={stats?.generatedAt ? formatRelativeTime(stats.generatedAt) : 'N/A'}
          icon={Clock}
          color="bg-purple-500"
          subtitle="Last updated"
        />
      </div>

      {/* Status Breakdown */}
      {stats?.statusStats && stats.statusStats.length > 0 && (
        <div className="card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Transaction Status Breakdown</h3>
            <span className="text-sm text-gray-500">
              {timeframeOptions.find(t => t.value === timeframe)?.label}
            </span>
          </div>
          
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-5">
            {stats.statusStats.map((status) => {
              const statusInfo = getStatusBadge(status._id);
              const percentage = stats.recentTransactions > 0 ? 
                Math.round((status.count / stats.recentTransactions) * 100) : 0;
              
              return (
                <div key={status._id} className="text-center">
                  <div className="mb-2">
                    <div className="text-2xl font-bold text-gray-900">
                      {status.count.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">
                      {percentage}%
                    </div>
                  </div>
                  <span className={`badge ${statusInfo.className}`}>
                    {statusInfo.label}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Visual representation */}
          <div className="mt-6">
            <div className="flex rounded-lg overflow-hidden h-4">
              {stats.statusStats.map((status) => {
                const statusInfo = getStatusBadge(status._id);
                const percentage = stats.recentTransactions > 0 ? 
                  (status.count / stats.recentTransactions) * 100 : 0;
                
                const colorMap = {
                  'badge-success': 'bg-green-500',
                  'badge-info': 'bg-blue-500',
                  'badge-warning': 'bg-yellow-500',
                  'badge-danger': 'bg-red-500'
                };
                
                return (
                  <div
                    key={status._id}
                    className={colorMap[statusInfo.className] || 'bg-gray-500'}
                    style={{ width: `${percentage}%` }}
                    title={`${statusInfo.label}: ${status.count} (${Math.round(percentage)}%)`}
                  />
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Top Senders and Recipients */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <TopListCard
          title={`Top Senders (${timeframeOptions.find(t => t.value === timeframe)?.label})`}
          items={stats?.topSenders}
          icon={Users}
          emptyMessage="No sender data available for this period"
        />
        
        <TopListCard
          title={`Top Recipients (${timeframeOptions.find(t => t.value === timeframe)?.label})`}
          items={stats?.topRecipients}
          icon={Globe}
          emptyMessage="No recipient data available for this period"
        />
      </div>

      {/* Additional Info */}
      <div className="card p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Report Information</h3>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div>
            <dt className="text-sm font-medium text-gray-500">Report Period</dt>
            <dd className="mt-1 text-sm text-gray-900">
              {timeframeOptions.find(t => t.value === timeframe)?.label}
            </dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Generated At</dt>
            <dd className="mt-1 text-sm text-gray-900">
              {stats?.generatedAt ? formatDate(stats.generatedAt) : 'N/A'}
            </dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Data Source</dt>
            <dd className="mt-1 text-sm text-gray-900">MongoDB Email Logs</dd>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Statistics;
