#!/usr/bin/env node

// Test script for MongoDB logging functionality
const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

console.log('Testing MongoDB Email Logging Setup...\n');

const config = {
    host: 'localhost',
    port: 27017,
    database: 'haraka_email_logs'
};

async function runTests() {
    console.log('1. Testing MongoDB connection...');
    
    const connection_string = `mongodb://${config.host}:${config.port}/${config.database}`;
    let client;
    
    try {
        client = await MongoClient.connect(connection_string, {
            connectTimeoutMS: 5000,
            socketTimeoutMS: 10000
        });
        
        const db = client.db(config.database);
        console.log('   ✅ Successfully connected to MongoDB');
        console.log('   📊 Database:', config.database);
        
        // Test collections
        console.log('\n2. Testing collections...');
        const collections = await db.listCollections().toArray();
        const collection_names = collections.map(c => c.name);
        
        const expected_collections = [
            'email_transactions',
            'email_connections',
            'email_errors',
            'email_stats'
        ];
        
        expected_collections.forEach(name => {
            if (collection_names.includes(name)) {
                console.log(`   ✅ Collection exists: ${name}`);
            } else {
                console.log(`   ⚠️  Collection missing: ${name}`);
            }
        });
        
        // Test indexes
        console.log('\n3. Testing indexes...');
        const transactions = db.collection('email_transactions');
        const indexes = await transactions.listIndexes().toArray();
        
        console.log(`   📋 Found ${indexes.length} indexes:`);
        indexes.forEach(index => {
            console.log(`     - ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        // Test sample data insertion
        console.log('\n4. Testing data insertion...');
        const sample_transaction = {
            transaction_id: 'test-' + Date.now(),
            timestamp: new Date(),
            connection: {
                remote_ip: '*************',
                remote_host: 'test.example.com'
            },
            mail_from: {
                address: '<EMAIL>'
            },
            rcpt_to: [{
                address: '<EMAIL>'
            }],
            status: 'test',
            processing_time: 100
        };
        
        const result = await transactions.insertOne(sample_transaction);
        console.log('   ✅ Test document inserted with ID:', result.insertedId);
        
        // Test querying
        console.log('\n5. Testing data retrieval...');
        const count = await transactions.countDocuments();
        console.log(`   📊 Total documents in collection: ${count}`);
        
        const recent = await transactions.find()
            .sort({ timestamp: -1 })
            .limit(5)
            .toArray();
        
        console.log(`   📋 Recent transactions (${recent.length}):`);
        recent.forEach(doc => {
            console.log(`     - ${doc.transaction_id}: ${doc.status} (${doc.timestamp})`);
        });
        
        // Clean up test data
        await transactions.deleteOne({ _id: result.insertedId });
        console.log('   🧹 Test document cleaned up');
        
    } catch (error) {
        console.error('   ❌ MongoDB test failed:', error.message);
        return false;
    } finally {
        if (client) {
            await client.close();
        }
    }
    
    // Test plugin configuration
    console.log('\n6. Testing plugin configuration...');
    const config_file = './config/mongodb_logger.ini';
    
    if (fs.existsSync(config_file)) {
        console.log('   ✅ Configuration file exists');
        
        const config_content = fs.readFileSync(config_file, 'utf8');
        
        // Check key configuration options
        const checks = [
            { key: 'enabled=true', desc: 'Plugin enabled' },
            { key: 'host=localhost', desc: 'MongoDB host configured' },
            { key: 'database=haraka_email_logs', desc: 'Database name configured' },
            { key: 'log_all_transactions=true', desc: 'All transactions logging enabled' }
        ];
        
        checks.forEach(check => {
            if (config_content.includes(check.key)) {
                console.log(`   ✅ ${check.desc}`);
            } else {
                console.log(`   ⚠️  ${check.desc} - not found`);
            }
        });
        
    } else {
        console.log('   ❌ Configuration file not found:', config_file);
    }
    
    // Test plugin file
    console.log('\n7. Testing plugin file...');
    const plugin_file = './plugins/mongodb_logger.js';
    
    if (fs.existsSync(plugin_file)) {
        console.log('   ✅ Plugin file exists');
        
        try {
            const plugin = require(path.resolve(plugin_file));
            
            const required_functions = [
                'register',
                'load_config',
                'connect_mongodb',
                'log_connection',
                'log_data_complete',
                'log_queue',
                'shutdown'
            ];
            
            required_functions.forEach(func => {
                if (typeof plugin[func] === 'function') {
                    console.log(`   ✅ Function exists: ${func}`);
                } else {
                    console.log(`   ❌ Function missing: ${func}`);
                }
            });
            
        } catch (error) {
            console.log('   ❌ Error loading plugin:', error.message);
        }
    } else {
        console.log('   ❌ Plugin file not found:', plugin_file);
    }
    
    // Test plugins configuration
    console.log('\n8. Testing plugins configuration...');
    const plugins_file = './config/plugins';
    
    if (fs.existsSync(plugins_file)) {
        const plugins_content = fs.readFileSync(plugins_file, 'utf8');
        
        if (plugins_content.includes('mongodb_logger')) {
            console.log('   ✅ mongodb_logger is enabled in plugins configuration');
        } else {
            console.log('   ⚠️  mongodb_logger is NOT enabled in plugins configuration');
            console.log('   💡 Add "mongodb_logger" to config/plugins to enable');
        }
    } else {
        console.log('   ❌ Plugins configuration file not found');
    }
    
    console.log('\n✅ MongoDB logging test completed!');
    console.log('\nNext steps:');
    console.log('1. Ensure MongoDB is running: sudo systemctl start mongod');
    console.log('2. Run schema setup: node mongodb_schema.js');
    console.log('3. Add "mongodb_logger" to config/plugins');
    console.log('4. Restart Haraka to load the plugin');
    console.log('5. Send test emails and check the database');
    
    return true;
}

// Check if MongoDB module is available
try {
    require('mongodb');
    runTests();
} catch (error) {
    console.error('❌ MongoDB module not found. Install it with:');
    console.error('   npm install mongodb');
    process.exit(1);
}
