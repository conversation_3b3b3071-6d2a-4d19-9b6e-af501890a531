# Dynamic IP Privacy Solution for Haraka

Complete solution for automatically handling dynamic IP changes and stripping IP-revealing headers from emails sent through your Haraka relay server.

## 🎯 **Problem Solved**

1. **Dynamic IP Management**: Automatically updates relay ACL when `hallocktest6.tmv.co.il` IP changes
2. **IP Privacy Protection**: Strips ALL headers revealing your home server IP address
3. **SPF Issues**: Eliminates SPF failures for relayed emails
4. **Configurable**: Fully configurable through INI files

## 🔧 **Components Installed**

### **1. Dynamic IP Updater Plugin**
- **File**: `plugins/dynamic_ip_updater.js`
- **Config**: `config/dynamic_ip_updater.ini`
- **Purpose**: Monitors `hallocktest6.tmv.co.il` and updates `relay_acl_allow`

### **2. Aggressive IP Stripper Plugin**
- **File**: `plugins/aggressive_ip_stripper.js`
- **Config**: `config/aggressive_ip_stripper.ini`
- **Purpose**: Strips ANY header containing your dynamic IP

### **3. Enhanced Header Stripper Plugin**
- **File**: `plugins/strip_relay_headers.js`
- **Config**: `config/strip_relay_headers.ini`
- **Purpose**: Backup header stripping with specific header type targeting

### **4. SPF Configuration**
- **Modified**: `config/plugins` (SPF disabled)
- **Modified**: `config/spf.ini` (skip relaying)
- **Purpose**: Prevents SPF failures for relayed emails

## ⚙️ **Configuration Files**

### **Dynamic IP Updater** (`config/dynamic_ip_updater.ini`)
```ini
[main]
enabled=true
hostname=hallocktest6.tmv.co.il
check_interval=300
relay_acl_file=relay_acl_allow
backup_old_config=true
```

### **Aggressive IP Stripper** (`config/aggressive_ip_stripper.ini`)
```ini
[main]
enabled=true
hostname=hallocktest6.tmv.co.il
ip_refresh_interval=300
domain=mosh.wtf
aggressive_mode=true
debug_logging=true

[headers_to_always_strip]
# Add headers to always strip
# X-Source-IP=1

[ip_patterns]
# Add regex patterns for additional IPs to strip
# private_192=192\.168\.\d+\.\d+
```

## 🚀 **How It Works**

### **Dynamic IP Monitoring**
1. **Every 5 minutes**: Resolves `hallocktest6.tmv.co.il` to get current IP
2. **IP Change Detection**: Compares with previous IP
3. **Automatic Update**: Updates `relay_acl_allow` with new IP/32
4. **Backup**: Creates timestamped backup of old config
5. **Reload**: Attempts to reload relay configuration

### **Header Stripping Process**
1. **IP Resolution**: Resolves current IP of `hallocktest6.tmv.co.il`
2. **Email Processing**: Intercepts emails at multiple hook points
3. **Header Scanning**: Checks ALL headers for IP or hostname
4. **Aggressive Removal**: Strips ANY header containing your IP
5. **Clean Output**: Email sent with only clean headers

### **Headers Stripped**
- ✅ `Received: from hallocktest6.tmv.co.il ([*************])`
- ✅ `Received-SPF: Fail (...*************...)`
- ✅ `Authentication-Results: ...client-ip=*************`
- ✅ `X-Originating-IP: *************`
- ✅ `X-Forwarded-For: *************`
- ✅ Any header containing your current IP or hostname

## 📋 **Testing & Verification**

### **Test Scripts Available**
- `./test_dynamic_ip.js` - Test IP updater functionality
- `./test_aggressive_stripper.js` - Test header stripper
- `./analyze_email_headers.js <email.eml>` - Analyze received emails
- `./update_relay_ip.js` - Manual IP update

### **Verification Steps**
1. **Check IP Resolution**:
   ```bash
   nslookup hallocktest6.tmv.co.il
   ```

2. **Check Relay ACL**:
   ```bash
   cat config/relay_acl_allow
   ```

3. **Check Logs**:
   ```bash
   grep "Aggressive IP Stripper" /var/log/haraka.log
   grep "Dynamic IP Updater" /var/log/haraka.log
   ```

4. **Test Email Headers**:
   ```bash
   ./analyze_email_headers.js received_email.eml
   ```

## 🔍 **Log Messages to Watch For**

### **Dynamic IP Updater**
```
[INFO] Dynamic IP Updater plugin initialized
[INFO] Starting IP monitoring for hallocktest6.tmv.co.il every 300 seconds
[INFO] Initial IP for hallocktest6.tmv.co.il: *************
[INFO] IP change detected: ************* -> *************
[INFO] Successfully updated relay_acl_allow with new IP: *************
```

### **Aggressive IP Stripper**
```
[INFO] Aggressive IP Stripper plugin loading...
[INFO] Home server IP resolved to: *************
[INFO] === AGGRESSIVE IP STRIPPER RUNNING ===
[INFO] STRIPPING HEADER: received-spf (contains home IP: *************)
[INFO] === STRIPPED 3 HEADERS ===
```

## 🛠️ **Maintenance**

### **When IP Changes**
- **Automatic**: Both plugins detect and handle IP changes automatically
- **Manual**: Run `./update_relay_ip.js` if needed
- **Verification**: Check logs and test email headers

### **Configuration Updates**
- **Hostname Change**: Update both `dynamic_ip_updater.ini` and `aggressive_ip_stripper.ini`
- **Timing**: Adjust `check_interval` and `ip_refresh_interval` as needed
- **Logging**: Enable/disable `debug_logging` for troubleshooting

### **Troubleshooting**
1. **Plugin Not Loading**: Check syntax with `node -c plugins/plugin_name.js`
2. **IP Not Updating**: Check DNS resolution and network connectivity
3. **Headers Still Leaking**: Enable debug logging and check plugin execution
4. **Performance Issues**: Increase check intervals or disable debug logging

## 📁 **File Structure**
```
/home/<USER>/haraka/
├── plugins/
│   ├── dynamic_ip_updater.js
│   ├── strip_relay_headers.js
│   └── aggressive_ip_stripper.js
├── config/
│   ├── dynamic_ip_updater.ini
│   ├── strip_relay_headers.ini
│   ├── aggressive_ip_stripper.ini
│   ├── plugins (updated)
│   ├── spf.ini (updated)
│   └── relay_acl_allow (auto-updated)
├── test_dynamic_ip.js
├── test_aggressive_stripper.js
├── update_relay_ip.js
├── analyze_email_headers.js
└── DYNAMIC_IP_PRIVACY_SOLUTION.md (this file)
```

## ✅ **Success Criteria**

Your setup is working correctly when:
- ✅ Emails relay successfully from `hallocktest6.tmv.co.il`
- ✅ No IP address visible in received email headers
- ✅ No SPF failures in logs
- ✅ Automatic IP updates when your IP changes
- ✅ Clean headers showing only `mosh.wtf` domain

## 🔄 **Next Steps**

1. **Restart Haraka** to load all plugins and configurations
2. **Send test email** from your home server
3. **Analyze headers** of received email
4. **Monitor logs** for first 24 hours to ensure smooth operation
5. **Adjust configuration** if needed based on logs

Your email privacy is now fully protected with automatic dynamic IP handling! 🛡️
