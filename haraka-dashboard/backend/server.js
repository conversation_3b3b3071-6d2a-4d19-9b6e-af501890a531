const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { MongoClient } = require('mongodb');
const { AuthManager, authenticateToken, requireAdmin } = require('./auth');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// MongoDB connection
let db;
let client;
let authManager;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(cors({
  //origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// MongoDB connection function
async function connectToMongoDB() {
  try {
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/haraka_email_logs';
    client = new MongoClient(uri, {
      connectTimeoutMS: 10000,
      socketTimeoutMS: 30000,
      maxPoolSize: 10
    });
    
    await client.connect();
    db = client.db(process.env.MONGODB_DB || 'haraka_email_logs');

    console.log('✅ Connected to MongoDB successfully');

    // Test the connection
    await db.admin().ping();
    console.log('✅ MongoDB ping successful');

    // Initialize authentication manager
    authManager = new AuthManager(db);
    await authManager.initializeDefaultAdmin();
    console.log('✅ Authentication system initialized');
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
}

// Health check endpoint (public)
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    mongodb: db ? 'connected' : 'disconnected',
    authentication: authManager ? 'enabled' : 'disabled'
  });
});

// Authentication routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    const result = await authManager.authenticateUser(username, password);

    if (result.success) {
      res.json({
        success: true,
        token: result.token,
        user: result.user
      });
    } else {
      res.status(401).json({ error: result.message });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

app.post('/api/auth/logout', authenticateToken(authManager), async (req, res) => {
  try {
    const token = req.headers['authorization']?.split(' ')[1];
    await authManager.logout(token);
    res.json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
});

app.get('/api/auth/me', authenticateToken(authManager), (req, res) => {
  res.json({
    success: true,
    user: req.user
  });
});

// Get email transactions with pagination, sorting, and filtering (PROTECTED)
app.get('/api/transactions', authenticateToken(authManager), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      sortBy = 'timestamp',
      sortOrder = 'desc',
      search = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build filter query
    const filter = {};
    
    // Search filter (search in sender, recipient, subject)
    if (search) {
      filter.$or = [
        { 'mail_from.address': { $regex: search, $options: 'i' } },
        { 'rcpt_to.address': { $regex: search, $options: 'i' } },
        { 'message.subject': { $regex: search, $options: 'i' } },
        { 'headers.from': { $regex: search, $options: 'i' } },
        { 'headers.to': { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      filter.status = status;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      filter.timestamp = {};
      if (dateFrom) {
        filter.timestamp.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        filter.timestamp.$lte = new Date(dateTo);
      }
    }

    // Sort configuration
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute queries
    const [transactions, totalCount] = await Promise.all([
      db.collection('email_transactions')
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .toArray(),
      db.collection('email_transactions').countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    res.json({
      transactions,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit: limitNum
      }
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({
      error: 'Failed to fetch transactions',
      message: error.message
    });
  }
});

// Get transaction statistics (PROTECTED)
app.get('/api/stats', authenticateToken(authManager), async (req, res) => {
  try {
    const { timeframe = '24h' } = req.query;
    
    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case '1h':
        startDate = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const [
      totalTransactions,
      recentTransactions,
      statusStats,
      topSenders,
      topRecipients
    ] = await Promise.all([
      // Total transactions count
      db.collection('email_transactions').countDocuments(),
      
      // Recent transactions count
      db.collection('email_transactions').countDocuments({
        timestamp: { $gte: startDate }
      }),
      
      // Status statistics
      db.collection('email_transactions').aggregate([
        { $match: { timestamp: { $gte: startDate } } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]).toArray(),
      
      // Top senders
      db.collection('email_transactions').aggregate([
        { $match: { timestamp: { $gte: startDate } } },
        { $group: { _id: '$mail_from.address', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]).toArray(),
      
      // Top recipients
      db.collection('email_transactions').aggregate([
        { $match: { timestamp: { $gte: startDate } } },
        { $unwind: '$rcpt_to' },
        { $group: { _id: '$rcpt_to.address', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]).toArray()
    ]);

    res.json({
      timeframe,
      totalTransactions,
      recentTransactions,
      statusStats,
      topSenders,
      topRecipients,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

// Get single transaction by ID (PROTECTED)
app.get('/api/transactions/:id', authenticateToken(authManager), async (req, res) => {
  try {
    const { id } = req.params;
    
    // Try to find by MongoDB ObjectId or transaction_id
    let transaction;
    
    try {
      const { ObjectId } = require('mongodb');
      transaction = await db.collection('email_transactions').findOne({
        _id: new ObjectId(id)
      });
    } catch (err) {
      // If ObjectId fails, try transaction_id
      transaction = await db.collection('email_transactions').findOne({
        transaction_id: id
      });
    }

    if (!transaction) {
      return res.status(404).json({
        error: 'Transaction not found'
      });
    }

    res.json(transaction);

  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({
      error: 'Failed to fetch transaction',
      message: error.message
    });
  }
});

// Admin management routes (PROTECTED - ADMIN ONLY)
app.get('/api/admin/users', authenticateToken(authManager), requireAdmin, async (req, res) => {
  try {
    const result = await authManager.listUsers();
    if (result.success) {
      res.json(result.users);
    } else {
      res.status(500).json({ error: result.message });
    }
  } catch (error) {
    console.error('List users error:', error);
    res.status(500).json({ error: 'Failed to list users' });
  }
});

app.post('/api/admin/users', authenticateToken(authManager), requireAdmin, async (req, res) => {
  try {
    const { username, password, role } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    const result = await authManager.createUser(username, password, role || 'admin');

    if (result.success) {
      res.json(result.user);
    } else {
      res.status(400).json({ error: result.message });
    }
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

app.delete('/api/admin/users/:id', authenticateToken(authManager), requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent deleting yourself
    if (id === req.user.userId) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    const result = await authManager.deleteUser(id);

    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: result.message });
    }
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

app.post('/api/admin/change-password', authenticateToken(authManager), async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current password and new password required' });
    }

    const result = await authManager.changePassword(req.user.userId, currentPassword, newPassword);

    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: result.message });
    }
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ error: 'Failed to change password' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found'
  });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🔄 Shutting down gracefully...');
  if (client) {
    await client.close();
    console.log('✅ MongoDB connection closed');
  }
  process.exit(0);
});

// Start server
async function startServer() {
  await connectToMongoDB();
  
  app.listen(PORT, () => {
    console.log(`🚀 Haraka Dashboard API server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📧 Transactions API: http://localhost:${PORT}/api/transactions`);
  });
}

startServer().catch(console.error);
