[{"/home/<USER>/haraka/haraka-dashboard/frontend/src/index.js": "1", "/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js": "2", "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js": "3", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Dashboard.js": "4", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js": "5", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js": "6", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Statistics.js": "7", "/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js": "8", "/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js": "9"}, {"size": 254, "mtime": 1753041207794, "results": "10", "hashOfConfig": "11"}, {"size": 752, "mtime": 1753041217090, "results": "12", "hashOfConfig": "11"}, {"size": 5663, "mtime": 1753041251363, "results": "13", "hashOfConfig": "11"}, {"size": 10851, "mtime": 1753041343965, "results": "14", "hashOfConfig": "11"}, {"size": 17064, "mtime": 1753041472105, "results": "15", "hashOfConfig": "11"}, {"size": 10472, "mtime": 1753041520655, "results": "16", "hashOfConfig": "11"}, {"size": 10652, "mtime": 1753041569425, "results": "17", "hashOfConfig": "11"}, {"size": 3847, "mtime": 1753042915190, "results": "18", "hashOfConfig": "11"}, {"size": 4095, "mtime": 1753041297108, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "esj15b", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/haraka/haraka-dashboard/frontend/src/index.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Dashboard.js", ["47"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js", ["48", "49"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js", ["50", "51"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Statistics.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js", ["52", "53", "54"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js", [], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 9, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 9, "endColumn": 14}, {"ruleId": "55", "severity": 1, "message": "59", "line": 9, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 9, "endColumn": 11}, {"ruleId": "60", "severity": 1, "message": "61", "line": 49, "column": 6, "nodeType": "62", "endLine": 49, "endColumn": 15, "suggestions": "63"}, {"ruleId": "55", "severity": 1, "message": "64", "line": 10, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 10, "endColumn": 7}, {"ruleId": "55", "severity": 1, "message": "65", "line": 11, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 11, "endColumn": 8}, {"ruleId": "60", "severity": 1, "message": "66", "line": 53, "column": 6, "nodeType": "62", "endLine": 53, "endColumn": 11, "suggestions": "67"}, {"ruleId": "60", "severity": 1, "message": "68", "line": 88, "column": 6, "nodeType": "62", "endLine": 88, "endColumn": 8, "suggestions": "69"}, {"ruleId": "60", "severity": 1, "message": "70", "line": 118, "column": 6, "nodeType": "62", "endLine": 118, "endColumn": 17, "suggestions": "71"}, "no-unused-vars", "'AlertCircle' is defined but never used.", "Identifier", "unusedVar", "'Calendar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'refetch'. Either include it or remove the dependency array.", "ArrayExpression", ["72"], "'User' is defined but never used.", "'Globe' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["73"], "React Hook useEffect has a missing dependency: 'fetchTransactions'. Either include it or remove the dependency array.", ["74"], "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", ["75"], {"desc": "76", "fix": "77"}, {"desc": "78", "fix": "79"}, {"desc": "80", "fix": "81"}, {"desc": "82", "fix": "83"}, "Update the dependencies array to be: [filters, refetch]", {"range": "84", "text": "85"}, "Update the dependencies array to be: [fetchData, url]", {"range": "86", "text": "87"}, "Update the dependencies array to be: [fetchTransactions]", {"range": "88", "text": "89"}, "Update the dependencies array to be: [fetchStats, timeframe]", {"range": "90", "text": "91"}, [990, 999], "[filters, refetch]", [1246, 1251], "[fetchData, url]", [2208, 2210], "[fetchTransactions]", [2976, 2987], "[fetchStats, timeframe]"]