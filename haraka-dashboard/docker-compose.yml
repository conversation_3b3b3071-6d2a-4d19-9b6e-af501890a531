version: '3.8'

services:
  # Backend API
  haraka-dashboard-api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: haraka-dashboard-api
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=mongodb://host.docker.internal:27017/haraka_email_logs
      - MONGODB_DB=haraka_email_logs
      - CORS_ORIGIN=http://localhost:3000
    ports:
      - "3001:3001"
    networks:
      - haraka-network
    depends_on:
      - mongodb
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Dashboard
  haraka-dashboard-web:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: haraka-dashboard-web
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://localhost:3001/api
    ports:
      - "3000:80"
    networks:
      - haraka-network
    depends_on:
      - haraka-dashboard-api

  # MongoDB (for development - in production, use existing MongoDB)
  mongodb:
    image: mongo:7.0
    container_name: haraka-mongodb
    restart: unless-stopped
    environment:
      - MONGO_INITDB_DATABASE=haraka_email_logs
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init:/docker-entrypoint-initdb.d
    networks:
      - haraka-network

networks:
  haraka-network:
    driver: bridge

volumes:
  mongodb_data:
