{"name": "haraka-dashboard-backend", "version": "1.0.0", "description": "Backend API for Haraka Email Server Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["haraka", "email", "dashboard", "mongodb"], "author": "Haraka Dashboard", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "mongodb": "^6.17.0", "dotenv": "^16.3.1", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-session": "^1.17.3", "connect-mongo": "^5.1.0", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}