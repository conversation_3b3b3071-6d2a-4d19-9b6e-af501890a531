# Haraka Email Dashboard - Apache2 Configuration for Subdirectory
# This configuration serves the dashboard from /test subdirectory on existing domain

# Add this to your existing virtual host configuration
# Or create a new alias configuration

# Option 1: Add to existing tmv.co.il virtual host
# Add these directives inside your existing <VirtualHost> block:

# Alias for Haraka Dashboard
Alias /haraka-dashboard /var/www/tmv.co.il/test
Alias /dashboard /var/www/tmv.co.il/test

# Directory configuration for dashboard
<Directory "/var/www/tmv.co.il/test">
    Options -Indexes +FollowSymLinks
    AllowOverride None
    Require all granted
    
    # Enable mod_rewrite for React Router
    RewriteEngine On
    
    # Handle React Router - serve index.html for all non-file requests within this directory
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule ^(.*)$ /test/index.html [L]
    
    # Security headers
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "no-referrer-when-downgrade"
    
    # Cache static assets
    <FilesMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </FilesMatch>
    
    # Don't cache HTML files
    <FilesMatch "\.html$">
        ExpiresActive On
        ExpiresDefault "access plus 0 seconds"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
    </FilesMatch>
</Directory>

# API proxy configuration (add to main virtual host)
# Proxy API requests to Node.js backend
ProxyPreserveHost On
ProxyRequests Off

# API proxy - adjust the path as needed
ProxyPass /api/ http://localhost:3001/api/
ProxyPassReverse /api/ http://localhost:3001/api/

# Alternative: If you want API under /haraka-api/
# ProxyPass /haraka-api/ http://localhost:3001/api/
# ProxyPassReverse /haraka-api/ http://localhost:3001/api/

# Option 2: Complete standalone configuration for subdomain
# Use this if you want to create a separate subdomain

#<VirtualHost *:80>
#    ServerAdmin <EMAIL>
#    ServerName dashboard.tmv.co.il
#    DocumentRoot /var/www/tmv.co.il/test
#    
#    # Enable mod_rewrite for React Router
#    RewriteEngine On
#    
#    # Security headers
#    Header always set X-Frame-Options "SAMEORIGIN"
#    Header always set X-XSS-Protection "1; mode=block"
#    Header always set X-Content-Type-Options "nosniff"
#    Header always set Referrer-Policy "no-referrer-when-downgrade"
#    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src 'self' http://s3.tmv.co.il:3001"
#    
#    # Proxy API requests to Node.js backend
#    ProxyPreserveHost On
#    ProxyRequests Off
#    ProxyPass /api/ http://localhost:3001/api/
#    ProxyPassReverse /api/ http://localhost:3001/api/
#    
#    # Handle React Router
#    RewriteCond %{REQUEST_FILENAME} !-f
#    RewriteCond %{REQUEST_FILENAME} !-d
#    RewriteCond %{REQUEST_URI} !^/api/
#    RewriteRule . /index.html [L]
#    
#    # Directory configuration
#    <Directory /var/www/tmv.co.il/test>
#        Options -Indexes +FollowSymLinks
#        AllowOverride None
#        Require all granted
#    </Directory>
#    
#    # Logging
#    ErrorLog ${APACHE_LOG_DIR}/dashboard.tmv.co.il_error.log
#    CustomLog ${APACHE_LOG_DIR}/dashboard.tmv.co.il_access.log combined
#</VirtualHost>

# Required Apache modules (make sure these are enabled):
# sudo a2enmod rewrite
# sudo a2enmod headers
# sudo a2enmod proxy
# sudo a2enmod proxy_http
# sudo a2enmod expires

# To enable this configuration:
# 1. Copy this file to /etc/apache2/sites-available/
# 2. Enable the site: sudo a2ensite haraka-dashboard-subdirectory
# 3. Reload Apache: sudo systemctl reload apache2
