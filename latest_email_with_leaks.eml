Delivered-To: <EMAIL>
Received: by 2002:a17:907:1c9f:b0:aec:6680:52d6 with SMTP id nb31csp1259665ejc;
        Sun, 20 Jul 2025 05:11:51 -0700 (PDT)
X-Google-Smtp-Source: AGHT+IEjqQGcWG/lPQ86ohlniLwpt9qPs7Qbk6mnho+bGXG+VQzRz/+JXJe0kcGpPFnFYulAXhJE
X-Received: by 2002:a05:6402:270e:b0:607:e83a:d698 with SMTP id 4fb4d7f45d1cf-6128592cb8dmr13848461a12.2.1753013511687;
        Sun, 20 Jul 2025 05:11:51 -0700 (PDT)
ARC-Seal: i=1; a=rsa-sha256; t=1753013511; cv=none;
        d=google.com; s=arc-20240605;
        b=FfHBtfK3gArZfPaGTip/qrn3ZOTjwJyla6iy8GXurcVrnBAm6N67QoXvn2RrbovbiG
         7F2pCAUaZ5Qx8fgoEAVqOkwuDuMWaAAzSZf53Lovk2fJ7WUdd5cM5LkFCPNmPNd2ZdXQ
         27EDgQXf5gpzt5jf3la86i6lg6bCAYRSnGiOUKVbaMSGLszHMxb0UcmlvF1Jh4tozgLx
         2Pv1zbzSyBFqZ0dnbGWz438LhV7hDk/8X6VZPQsINVkNwaahryvBHZzXBHOX4hLmPQd3
         4rpu7C3XrkGXIG69hRw2Af1g712yGGTmNiaxNZBzRDtVFoF0yBykmQyLFV7bDN0JPa5Y
         +FCw==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=content-transfer-encoding:to:message-id:thread-topic:subject:from
         :date:mime-version;
        bh=5ltYqKvnWbCBSsSpai8aDkW0akOloJ/liw19aL6DAJ4=;
        fh=ORY4Inv24sO9TIMCc81JflYNQoMcPWbSFxF8r3vXf8k=;
        b=LvncOv4h+MgeA775Z4fqWjpGz8KldF4PoosPH1/kDLZnnMJCaRvu/Yo69ra7u5GbUz
         luyfNAQ8G9D+EjsON1zHydnz62H0YsJoDa7toBQhfSzdf31i4fu90y5FR8eqMpNtHeBk
         UFxGWNITDsZ9CHGDUJGzQAHm8zZj/pJbErqDr7cO26gf5ju/ZxY66+ht6Cv3sojzARew
         He8Ogexz0lU11hBVyvqNl7AV4R+Oj3hKxFpolDa7m8DAeXtsLWpoPRga1IaT7z03ST14
         +1U+eH67xbYegClrzZisKCQ24OZiu9ped90sk/koD/AxJnYkFtT363De3qhbyN8SXMly
         kkvw==;
        dara=google.com
ARC-Authentication-Results: i=1; mx.google.com;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=mosh.wtf
Return-Path: <<EMAIL>>
Received: from aa5f5ebbc287 (vmi2705884.contaboserver.net. [*************])
        by mx.google.com with ESMTPS id 4fb4d7f45d1cf-612f15f5a8fsi1928748a12.180.2025.***********.51
        for <<EMAIL>>
        (version=TLS1_3 cipher=TLS_AES_256_GCM_SHA384 bits=256/256);
        Sun, 20 Jul 2025 05:11:51 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;
Authentication-Results: mx.google.com;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=mosh.wtf
Received: (Haraka outbound); Sun, 20 Jul 2025 12:11:51 +0000
Authentication-Results: aa5f5ebbc287; spf=fail smtp.mailfrom=mosh.wtf
Received-SPF: Fail (aa5f5ebbc287: domain of mosh.wtf does not designate ************* as permitted sender) receiver=aa5f5ebbc287; identity=mailfrom; client-ip=************* helo=mail.tmv.co.il; envelope-from=<<EMAIL>>
Received-SPF: None (aa5f5ebbc287: domain of mail.tmv.co.il does not designate ************* as permitted sender) receiver=aa5f5ebbc287; identity=helo; client-ip=************* helo=mail.tmv.co.il; envelope-from=<<EMAIL>>
Received: from mail.tmv.co.il (46-116-180-48.bb.netvision.net.il [*************]) by aa5f5ebbc287 (Haraka/3.1.1) with ESMTP id 505E582C-BAEA-4CE0-AC42-B559B2EB8BCB.1 envelope-from <<EMAIL>>; Sun, 20 Jul 2025 12:11:50 +0000
Received: from [127.0.0.1] (localhost [127.0.0.1]) by localhost (Mailerdaemon) with ESMTPSA id 57E564004B4 for <<EMAIL>>; Sun, 20 Jul 2025 12:11:40 +0000 (UTC)
MIME-Version: 1.0
Date: Sun, 20 Jul 2025 15:11:39 +0300
From: Moshe <<EMAIL>>
Subject: Bdica
Thread-Topic: Bdica
Message-ID: <<EMAIL>>
To: <<EMAIL>>
Content-Transfer-Encoding: base64
Content-Type: text/html; charset="utf-8"
X-Last-TLS-Session-Version: TLSv1.2
