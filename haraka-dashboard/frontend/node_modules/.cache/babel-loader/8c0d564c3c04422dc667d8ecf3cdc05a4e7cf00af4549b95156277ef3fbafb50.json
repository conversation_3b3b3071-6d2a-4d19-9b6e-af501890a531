{"ast": null, "code": "import React,{useState}from'react';import{Navigate}from'react-router-dom';import{Mail,Lock,AlertCircle,Eye,EyeOff}from'lucide-react';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{const[username,setUsername]=useState('');const[password,setPassword]=useState('');const[showPassword,setShowPassword]=useState(false);const[error,setError]=useState('');const[loading,setLoading]=useState(false);const{login,isAuthenticated}=useAuth();// Redirect if already authenticated\nif(isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true});}const handleSubmit=async e=>{e.preventDefault();setError('');setLoading(true);if(!username||!password){setError('Please enter both username and password');setLoading(false);return;}const result=await login(username,password);if(!result.success){setError(result.message);}setLoading(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sm:mx-auto sm:w-full sm:max-w-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center\",children:/*#__PURE__*/_jsx(Mail,{className:\"h-12 w-12 text-primary-600\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"mt-6 text-center text-3xl font-extrabold text-gray-900\",children:\"Haraka Dashboard\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-center text-sm text-gray-600\",children:\"Sign in to access the email server dashboard\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",children:[/*#__PURE__*/_jsxs(\"form\",{className:\"space-y-6\",onSubmit:handleSubmit,children:[error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400\"}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-3\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-800\",children:error})})]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"username\",className:\"block text-sm font-medium text-gray-700\",children:\"Username\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1\",children:/*#__PURE__*/_jsx(\"input\",{id:\"username\",name:\"username\",type:\"text\",autoComplete:\"username\",required:true,value:username,onChange:e=>setUsername(e.target.value),className:\"input\",placeholder:\"Enter your username\"})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",className:\"block text-sm font-medium text-gray-700\",children:\"Password\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-1 relative\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"password\",name:\"password\",type:showPassword?'text':'password',autoComplete:\"current-password\",required:true,value:password,onChange:e=>setPassword(e.target.value),className:\"input pr-10\",placeholder:\"Enter your password\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"absolute inset-y-0 right-0 pr-3 flex items-center\",onClick:()=>setShowPassword(!showPassword),children:showPassword?/*#__PURE__*/_jsx(EyeOff,{className:\"h-5 w-5 text-gray-400\"}):/*#__PURE__*/_jsx(Eye,{className:\"h-5 w-5 text-gray-400\"})})]})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,className:\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"}),\"Signing in...\"]}):'Sign in'})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full border-t border-gray-300\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"relative flex justify-center text-sm\",children:/*#__PURE__*/_jsx(\"span\",{className:\"px-2 bg-white text-gray-500\",children:\"Default credentials\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 bg-gray-50 rounded-md p-4\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Username:\"}),\" admin\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600 mt-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Password:\"}),\" Check server logs for generated password\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-2\",children:\"The password was displayed when the server first started. You can change it after logging in.\"})]})]})]})})]});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "Navigate", "Mail", "Lock", "AlertCircle", "Eye", "Eye<PERSON>ff", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "username", "setUsername", "password", "setPassword", "showPassword", "setShowPassword", "error", "setError", "loading", "setLoading", "login", "isAuthenticated", "to", "replace", "handleSubmit", "e", "preventDefault", "result", "success", "message", "className", "children", "onSubmit", "htmlFor", "id", "name", "type", "autoComplete", "required", "value", "onChange", "target", "placeholder", "onClick", "disabled"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { Mail, Lock, AlertCircle, Eye, EyeOff } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Login = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  \n  const { login, isAuthenticated } = useAuth();\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    if (!username || !password) {\n      setError('Please enter both username and password');\n      setLoading(false);\n      return;\n    }\n\n    const result = await login(username, password);\n    \n    if (!result.success) {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"flex justify-center\">\n          <Mail className=\"h-12 w-12 text-primary-600\" />\n        </div>\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n          Haraka Dashboard\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Sign in to access the email server dashboard\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"flex\">\n                  <AlertCircle className=\"h-5 w-5 text-red-400\" />\n                  <div className=\"ml-3\">\n                    <p className=\"text-sm text-red-800\">{error}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                Username\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  autoComplete=\"username\"\n                  required\n                  value={username}\n                  onChange={(e) => setUsername(e.target.value)}\n                  className=\"input\"\n                  placeholder=\"Enter your username\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"input pr-10\"\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <Eye className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Signing in...\n                  </div>\n                ) : (\n                  'Sign in'\n                )}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Default credentials</span>\n              </div>\n            </div>\n\n            <div className=\"mt-4 bg-gray-50 rounded-md p-4\">\n              <p className=\"text-sm text-gray-600\">\n                <strong>Username:</strong> admin\n              </p>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                <strong>Password:</strong> Check server logs for generated password\n              </p>\n              <p className=\"text-xs text-gray-500 mt-2\">\n                The password was displayed when the server first started. You can change it after logging in.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,OAASC,IAAI,CAAEC,IAAI,CAAEC,WAAW,CAAEC,GAAG,CAAEC,MAAM,KAAQ,cAAc,CACnE,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACe,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiB,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACmB,KAAK,CAAEC,QAAQ,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAEuB,KAAK,CAAEC,eAAgB,CAAC,CAAGjB,OAAO,CAAC,CAAC,CAE5C;AACA,GAAIiB,eAAe,CAAE,CACnB,mBAAOf,IAAA,CAACR,QAAQ,EAACwB,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACpC,CAEA,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBT,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CAACT,QAAQ,EAAI,CAACE,QAAQ,CAAE,CAC1BK,QAAQ,CAAC,yCAAyC,CAAC,CACnDE,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAP,KAAK,CAACV,QAAQ,CAAEE,QAAQ,CAAC,CAE9C,GAAI,CAACe,MAAM,CAACC,OAAO,CAAE,CACnBX,QAAQ,CAACU,MAAM,CAACE,OAAO,CAAC,CAC1B,CAEAV,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,mBACEX,KAAA,QAAKsB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eACzFvB,KAAA,QAAKsB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CzB,IAAA,QAAKwB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCzB,IAAA,CAACP,IAAI,EAAC+B,SAAS,CAAC,4BAA4B,CAAE,CAAC,CAC5C,CAAC,cACNxB,IAAA,OAAIwB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,kBAEvE,CAAI,CAAC,cACLzB,IAAA,MAAGwB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,8CAEtD,CAAG,CAAC,EACD,CAAC,cAENzB,IAAA,QAAKwB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDvB,KAAA,QAAKsB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DvB,KAAA,SAAMsB,SAAS,CAAC,WAAW,CAACE,QAAQ,CAAER,YAAa,CAAAO,QAAA,EAChDf,KAAK,eACJV,IAAA,QAAKwB,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cAC7DvB,KAAA,QAAKsB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzB,IAAA,CAACL,WAAW,EAAC6B,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAChDxB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBzB,IAAA,MAAGwB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEf,KAAK,CAAI,CAAC,CAC5C,CAAC,EACH,CAAC,CACH,CACN,cAEDR,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAO2B,OAAO,CAAC,UAAU,CAACH,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAE9E,CAAO,CAAC,cACRzB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBzB,IAAA,UACE4B,EAAE,CAAC,UAAU,CACbC,IAAI,CAAC,UAAU,CACfC,IAAI,CAAC,MAAM,CACXC,YAAY,CAAC,UAAU,CACvBC,QAAQ,MACRC,KAAK,CAAE7B,QAAS,CAChB8B,QAAQ,CAAGf,CAAC,EAAKd,WAAW,CAACc,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE,CAC7CT,SAAS,CAAC,OAAO,CACjBY,WAAW,CAAC,qBAAqB,CAClC,CAAC,CACC,CAAC,EACH,CAAC,cAENlC,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAO2B,OAAO,CAAC,UAAU,CAACH,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAE9E,CAAO,CAAC,cACRvB,KAAA,QAAKsB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzB,IAAA,UACE4B,EAAE,CAAC,UAAU,CACbC,IAAI,CAAC,UAAU,CACfC,IAAI,CAAEtB,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCuB,YAAY,CAAC,kBAAkB,CAC/BC,QAAQ,MACRC,KAAK,CAAE3B,QAAS,CAChB4B,QAAQ,CAAGf,CAAC,EAAKZ,WAAW,CAACY,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE,CAC7CT,SAAS,CAAC,aAAa,CACvBY,WAAW,CAAC,qBAAqB,CAClC,CAAC,cACFpC,IAAA,WACE8B,IAAI,CAAC,QAAQ,CACbN,SAAS,CAAC,mDAAmD,CAC7Da,OAAO,CAAEA,CAAA,GAAM5B,eAAe,CAAC,CAACD,YAAY,CAAE,CAAAiB,QAAA,CAE7CjB,YAAY,cACXR,IAAA,CAACH,MAAM,EAAC2B,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAE5CxB,IAAA,CAACJ,GAAG,EAAC4B,SAAS,CAAC,uBAAuB,CAAE,CACzC,CACK,CAAC,EACN,CAAC,EACH,CAAC,cAENxB,IAAA,QAAAyB,QAAA,cACEzB,IAAA,WACE8B,IAAI,CAAC,QAAQ,CACbQ,QAAQ,CAAE1B,OAAQ,CAClBY,SAAS,CAAC,mRAAmR,CAAAC,QAAA,CAE5Rb,OAAO,cACNV,KAAA,QAAKsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCzB,IAAA,QAAKwB,SAAS,CAAC,gEAAgE,CAAM,CAAC,gBAExF,EAAK,CAAC,CAEN,SACD,CACK,CAAC,CACN,CAAC,EACF,CAAC,cAEPtB,KAAA,QAAKsB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvB,KAAA,QAAKsB,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBzB,IAAA,QAAKwB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDzB,IAAA,QAAKwB,SAAS,CAAC,iCAAiC,CAAE,CAAC,CAChD,CAAC,cACNxB,IAAA,QAAKwB,SAAS,CAAC,sCAAsC,CAAAC,QAAA,cACnDzB,IAAA,SAAMwB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,CACrE,CAAC,EACH,CAAC,cAENvB,KAAA,QAAKsB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CvB,KAAA,MAAGsB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClCzB,IAAA,WAAAyB,QAAA,CAAQ,WAAS,CAAQ,CAAC,SAC5B,EAAG,CAAC,cACJvB,KAAA,MAAGsB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACvCzB,IAAA,WAAAyB,QAAA,CAAQ,WAAS,CAAQ,CAAC,4CAC5B,EAAG,CAAC,cACJzB,IAAA,MAAGwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+FAE1C,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}