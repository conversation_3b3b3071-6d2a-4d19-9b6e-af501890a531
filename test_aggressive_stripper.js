#!/usr/bin/env node

// Test script for the aggressive IP stripper plugin
const dns = require('dns');
const fs = require('fs');
const path = require('path');

console.log('Testing Aggressive IP Stripper Plugin...\n');

// Test configuration loading
console.log('1. Testing plugin configuration:');
const config_file = './config/aggressive_ip_stripper.ini';

if (fs.existsSync(config_file)) {
    console.log('   ✅ Configuration file exists');
    
    const config_content = fs.readFileSync(config_file, 'utf8');
    console.log('   Configuration preview:');
    
    const lines = config_content.split('\n').slice(0, 15);
    lines.forEach(line => {
        if (line.trim()) {
            console.log('     ' + line);
        }
    });
    
    // Parse hostname from config
    const hostname_match = config_content.match(/hostname\s*=\s*(.+)/);
    const hostname = hostname_match ? hostname_match[1].trim() : 'hallocktest6.tmv.co.il';
    
    console.log('\n2. Testing DNS resolution for: ' + hostname);
    
    dns.resolve4(hostname, (err, addresses) => {
        if (err) {
            console.error('   ❌ DNS resolution failed: ' + err.message);
            return;
        }
        
        if (!addresses || addresses.length === 0) {
            console.error('   ❌ No IP addresses found for ' + hostname);
            return;
        }
        
        const ip = addresses[0];
        console.log('   ✅ Resolved ' + hostname + ' to: ' + ip);
        
        console.log('\n3. Testing plugin file:');
        const plugin_file = './plugins/aggressive_ip_stripper.js';
        
        if (fs.existsSync(plugin_file)) {
            console.log('   ✅ Plugin file exists');
            
            // Test plugin loading
            try {
                const plugin = require(plugin_file);
                
                if (typeof plugin.register === 'function') {
                    console.log('   ✅ Plugin has register function');
                } else {
                    console.log('   ❌ Plugin missing register function');
                }
                
                if (typeof plugin.resolve_home_ip === 'function') {
                    console.log('   ✅ Plugin has resolve_home_ip function');
                } else {
                    console.log('   ❌ Plugin missing resolve_home_ip function');
                }
                
                if (typeof plugin.strip_ip_headers === 'function') {
                    console.log('   ✅ Plugin has strip_ip_headers function');
                } else {
                    console.log('   ❌ Plugin missing strip_ip_headers function');
                }
                
                console.log('   ✅ Plugin loaded successfully');
                
            } catch (err) {
                console.log('   ❌ Error loading plugin: ' + err.message);
            }
        } else {
            console.log('   ❌ Plugin file not found');
        }
        
        console.log('\n4. Testing plugins configuration:');
        const plugins_file = './config/plugins';
        
        if (fs.existsSync(plugins_file)) {
            const plugins_content = fs.readFileSync(plugins_file, 'utf8');
            
            if (plugins_content.includes('aggressive_ip_stripper')) {
                console.log('   ✅ aggressive_ip_stripper is enabled in plugins');
            } else {
                console.log('   ⚠️  aggressive_ip_stripper is NOT enabled in plugins');
            }
            
            if (plugins_content.includes('strip_relay_headers')) {
                console.log('   ✅ strip_relay_headers is enabled in plugins');
            } else {
                console.log('   ⚠️  strip_relay_headers is NOT enabled in plugins');
            }
            
            if (plugins_content.includes('# spf')) {
                console.log('   ✅ SPF plugin is disabled (commented out)');
            } else if (plugins_content.includes('spf')) {
                console.log('   ⚠️  SPF plugin is still enabled');
            } else {
                console.log('   ✅ SPF plugin is not in configuration');
            }
        } else {
            console.log('   ❌ plugins configuration file not found');
        }
        
        console.log('\n5. Testing header patterns with current IP:');
        testHeaderPatterns(ip, hostname);
        
        console.log('\n✅ Test completed!');
        console.log('\nTo use the plugin:');
        console.log('1. Restart Haraka to load the updated configuration');
        console.log('2. Send a test email from your home server');
        console.log('3. Check logs for: "AGGRESSIVE IP STRIPPER RUNNING"');
        console.log('4. Check logs for: "STRIPPING HEADER"');
        console.log('5. Verify received email headers are clean');
        
        console.log('\nConfiguration features:');
        console.log('- ✅ Dynamic IP resolution every 5 minutes');
        console.log('- ✅ Configurable hostname (' + hostname + ')');
        console.log('- ✅ Aggressive mode (strips ANY header with IP)');
        console.log('- ✅ Detailed logging for debugging');
        console.log('- ✅ Automatic IP change detection');
    });
    
} else {
    console.log('   ❌ Configuration file not found: ' + config_file);
}

function testHeaderPatterns(ip, hostname) {
    console.log('   Testing with IP: ' + ip + ' and hostname: ' + hostname);
    
    // Sample headers that should be stripped
    const test_headers = [
        'Received: from ' + hostname + ' ([' + ip + ']) by relay.example.com',
        'Received-SPF: Fail (domain does not designate ' + ip + ' as permitted sender)',
        'Authentication-Results: server; spf=fail smtp.mailfrom=<EMAIL> client-ip=' + ip,
        'X-Originating-IP: ' + ip,
        'X-Forwarded-For: ' + ip,
        'Received: from mail.server.com (' + hostname + ' [' + ip + ']) by destination'
    ];
    
    console.log('   Headers that should be stripped:');
    test_headers.forEach((header, index) => {
        const contains_ip = header.includes(ip);
        const contains_hostname = header.includes(hostname);
        const should_strip = contains_ip || contains_hostname;
        const status = should_strip ? '✅ STRIP' : '❌ KEEP';
        console.log('     ' + status + ': ' + header.substring(0, 80) + '...');
    });
    
    // Sample headers that should be kept
    const keep_headers = [
        'From: <EMAIL>',
        'To: <EMAIL>',
        'Subject: Test Message',
        'Date: Mon, 20 Jul 2025 10:00:00 +0000',
        'Message-ID: <<EMAIL>>',
        'Received: from external.server.com by destination.com'
    ];
    
    console.log('\n   Headers that should be kept:');
    keep_headers.forEach((header, index) => {
        const contains_ip = header.includes(ip);
        const contains_hostname = header.includes(hostname);
        const should_strip = contains_ip || contains_hostname;
        const status = should_strip ? '❌ STRIP' : '✅ KEEP';
        console.log('     ' + status + ': ' + header);
    });
}
