#!/usr/bin/env node

// Email Header Analysis Tool
// Analyzes email headers to check for IP leakage

const fs = require('fs');
const dns = require('dns');

const hostname = 'hallocktest6.tmv.co.il';

function analyzeHeaders(headerText, relayIP) {
    console.log('🔍 Analyzing email headers for IP leakage...\n');
    
    const lines = headerText.split('\n');
    const headers = [];
    let currentHeader = '';
    
    // Parse headers (handle multi-line headers)
    for (const line of lines) {
        if (line.match(/^\s/) && currentHeader) {
            // Continuation of previous header
            currentHeader += ' ' + line.trim();
        } else if (line.includes(':')) {
            // New header
            if (currentHeader) {
                headers.push(currentHeader);
            }
            currentHeader = line;
        }
    }
    if (currentHeader) {
        headers.push(currentHeader);
    }
    
    let leakageFound = false;
    let cleanHeaders = 0;
    let suspiciousHeaderCount = 0;
    
    console.log('📋 Header Analysis Results:\n');
    
    for (const header of headers) {
        const headerName = header.split(':')[0].toLowerCase();
        const headerValue = header.substring(header.indexOf(':') + 1).trim();
        
        let status = '✅ CLEAN';
        let reason = '';
        let isLeak = false;
        
        // Check for relay IP
        if (relayIP && header.includes(relayIP)) {
            status = '🚨 LEAK';
            reason = `Contains relay IP: ${relayIP}`;
            isLeak = true;
        }
        
        // Check for hostname
        if (header.includes(hostname)) {
            status = '🚨 LEAK';
            reason = `Contains hostname: ${hostname}`;
            isLeak = true;
        }
        
        // Check for private IPs
        const privateIPPatterns = [
            { pattern: /\b192\.168\.\d+\.\d+\b/, name: '192.168.x.x' },
            { pattern: /\b10\.\d+\.\d+\.\d+\b/, name: '10.x.x.x' },
            { pattern: /\b172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+\b/, name: '172.16-31.x.x' }
        ];
        
        for (const {pattern, name} of privateIPPatterns) {
            if (pattern.test(header)) {
                status = '⚠️  SUSPICIOUS';
                reason = `Contains private IP (${name})`;
                isLeak = true;
                break;
            }
        }
        
        // Check for suspicious header types
        const suspiciousHeaders = [
            'x-originating-ip',
            'x-forwarded-for',
            'x-real-ip',
            'x-source-ip',
            'x-client-ip',
            'x-remote-ip'
        ];
        
        if (suspiciousHeaders.includes(headerName)) {
            if (!isLeak) {
                status = '⚠️  SUSPICIOUS';
                reason = 'IP-revealing header type';
            }
            isLeak = true;
        }
        
        if (isLeak) {
            leakageFound = true;
            suspiciousHeaderCount++;
            console.log(`${status} ${headerName}: ${headerValue.substring(0, 80)}${headerValue.length > 80 ? '...' : ''}`);
            if (reason) console.log(`     └─ ${reason}`);
        } else {
            cleanHeaders++;
            if (process.argv.includes('--verbose')) {
                console.log(`${status} ${headerName}: ${headerValue.substring(0, 60)}${headerValue.length > 60 ? '...' : ''}`);
            }
        }
    }
    
    console.log('\n📊 Summary:');
    console.log(`   Total headers: ${headers.length}`);
    console.log(`   Clean headers: ${cleanHeaders}`);
    console.log(`   Suspicious headers: ${suspiciousHeaderCount}`);
    
    if (leakageFound) {
        console.log('\n🚨 IP LEAKAGE DETECTED!');
        console.log('   Your home server IP or identifying information is visible in the headers.');
        console.log('   Consider checking your Haraka header stripping configuration.');
    } else {
        console.log('\n✅ NO IP LEAKAGE DETECTED');
        console.log('   Headers appear clean and do not reveal your home server information.');
    }
    
    return !leakageFound;
}

function showUsage() {
    console.log('Email Header Analysis Tool');
    console.log('');
    console.log('Usage:');
    console.log('  node analyze_email_headers.js <email_file>');
    console.log('  node analyze_email_headers.js --stdin');
    console.log('');
    console.log('Options:');
    console.log('  --verbose    Show all headers (including clean ones)');
    console.log('  --stdin      Read email from standard input');
    console.log('  --help       Show this help message');
    console.log('');
    console.log('Examples:');
    console.log('  node analyze_email_headers.js email.eml');
    console.log('  cat email.eml | node analyze_email_headers.js --stdin');
    console.log('');
    console.log('The tool will analyze email headers for:');
    console.log('  - Your relay IP address');
    console.log('  - Your hostname (hallocktest6.tmv.co.il)');
    console.log('  - Private IP addresses');
    console.log('  - Suspicious header types');
}

function main() {
    if (process.argv.includes('--help')) {
        showUsage();
        return;
    }
    
    console.log('🔍 Email Header Analysis Tool\n');
    
    // Get relay IP first
    dns.resolve4(hostname, (err, addresses) => {
        let relayIP = null;
        
        if (err) {
            console.log(`⚠️  Could not resolve ${hostname}: ${err.message}`);
            console.log('   Continuing analysis without IP checking...\n');
        } else if (addresses && addresses.length > 0) {
            relayIP = addresses[0];
            console.log(`🌐 Resolved ${hostname} to: ${relayIP}\n`);
        }
        
        let emailContent = '';
        
        if (process.argv.includes('--stdin')) {
            // Read from stdin
            process.stdin.setEncoding('utf8');
            process.stdin.on('data', (chunk) => {
                emailContent += chunk;
            });
            process.stdin.on('end', () => {
                analyzeHeaders(emailContent, relayIP);
            });
        } else {
            // Read from file
            const filename = process.argv[2];
            if (!filename) {
                console.error('❌ Please provide an email file or use --stdin');
                console.log('   Use --help for usage information');
                process.exit(1);
            }
            
            try {
                emailContent = fs.readFileSync(filename, 'utf8');
                analyzeHeaders(emailContent, relayIP);
            } catch (error) {
                console.error(`❌ Error reading file ${filename}: ${error.message}`);
                process.exit(1);
            }
        }
    });
}

main();
