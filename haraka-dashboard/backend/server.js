const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { MongoClient } = require('mongodb');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// MongoDB connection
let db;
let client;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(cors({
  //origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// MongoDB connection function
async function connectToMongoDB() {
  try {
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/haraka_email_logs';
    client = new MongoClient(uri, {
      connectTimeoutMS: 10000,
      socketTimeoutMS: 30000,
      maxPoolSize: 10
    });
    
    await client.connect();
    db = client.db(process.env.MONGODB_DB || 'haraka_email_logs');
    
    console.log('✅ Connected to MongoDB successfully');
    
    // Test the connection
    await db.admin().ping();
    console.log('✅ MongoDB ping successful');
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    mongodb: db ? 'connected' : 'disconnected'
  });
});

// Get email transactions with pagination, sorting, and filtering
app.get('/api/transactions', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      sortBy = 'timestamp',
      sortOrder = 'desc',
      search = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build filter query
    const filter = {};
    
    // Search filter (search in sender, recipient, subject)
    if (search) {
      filter.$or = [
        { 'mail_from.address': { $regex: search, $options: 'i' } },
        { 'rcpt_to.address': { $regex: search, $options: 'i' } },
        { 'message.subject': { $regex: search, $options: 'i' } },
        { 'headers.from': { $regex: search, $options: 'i' } },
        { 'headers.to': { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      filter.status = status;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      filter.timestamp = {};
      if (dateFrom) {
        filter.timestamp.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        filter.timestamp.$lte = new Date(dateTo);
      }
    }

    // Sort configuration
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute queries
    const [transactions, totalCount] = await Promise.all([
      db.collection('email_transactions')
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .toArray(),
      db.collection('email_transactions').countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    res.json({
      transactions,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit: limitNum
      }
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({
      error: 'Failed to fetch transactions',
      message: error.message
    });
  }
});

// Get transaction statistics
app.get('/api/stats', async (req, res) => {
  try {
    const { timeframe = '24h' } = req.query;
    
    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case '1h':
        startDate = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const [
      totalTransactions,
      recentTransactions,
      statusStats,
      topSenders,
      topRecipients
    ] = await Promise.all([
      // Total transactions count
      db.collection('email_transactions').countDocuments(),
      
      // Recent transactions count
      db.collection('email_transactions').countDocuments({
        timestamp: { $gte: startDate }
      }),
      
      // Status statistics
      db.collection('email_transactions').aggregate([
        { $match: { timestamp: { $gte: startDate } } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]).toArray(),
      
      // Top senders
      db.collection('email_transactions').aggregate([
        { $match: { timestamp: { $gte: startDate } } },
        { $group: { _id: '$mail_from.address', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]).toArray(),
      
      // Top recipients
      db.collection('email_transactions').aggregate([
        { $match: { timestamp: { $gte: startDate } } },
        { $unwind: '$rcpt_to' },
        { $group: { _id: '$rcpt_to.address', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]).toArray()
    ]);

    res.json({
      timeframe,
      totalTransactions,
      recentTransactions,
      statusStats,
      topSenders,
      topRecipients,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

// Get single transaction by ID
app.get('/api/transactions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Try to find by MongoDB ObjectId or transaction_id
    let transaction;
    
    try {
      const { ObjectId } = require('mongodb');
      transaction = await db.collection('email_transactions').findOne({
        _id: new ObjectId(id)
      });
    } catch (err) {
      // If ObjectId fails, try transaction_id
      transaction = await db.collection('email_transactions').findOne({
        transaction_id: id
      });
    }

    if (!transaction) {
      return res.status(404).json({
        error: 'Transaction not found'
      });
    }

    res.json(transaction);

  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({
      error: 'Failed to fetch transaction',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found'
  });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🔄 Shutting down gracefully...');
  if (client) {
    await client.close();
    console.log('✅ MongoDB connection closed');
  }
  process.exit(0);
});

// Start server
async function startServer() {
  await connectToMongoDB();
  
  app.listen(PORT, () => {
    console.log(`🚀 Haraka Dashboard API server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📧 Transactions API: http://localhost:${PORT}/api/transactions`);
  });
}

startServer().catch(console.error);
