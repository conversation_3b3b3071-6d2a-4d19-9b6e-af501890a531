#!/usr/bin/env node

// Simple MongoDB connection test
const { MongoClient } = require('mongodb');

console.log('Testing MongoDB connection...\n');

const config = {
    host: 'localhost',
    port: 27017,
    database: 'haraka_email_logs'
};

async function testConnection() {
    console.log('1. Testing basic MongoDB connection...');
    
    const connection_string = `mongodb://${config.host}:${config.port}/${config.database}`;
    console.log('Connection string:', connection_string);
    
    let client;
    
    try {
        console.log('Attempting to connect with 5 second timeout...');
        client = await MongoClient.connect(connection_string, {
            connectTimeoutMS: 5000,
            socketTimeoutMS: 30000,
            serverSelectionTimeoutMS: 5000
        });
        
        console.log('✅ Successfully connected to MongoDB!');
        
        const db = client.db(config.database);
        console.log('✅ Database accessed:', config.database);
        
        // Test a simple operation
        const collections = await db.listCollections().toArray();
        console.log('✅ Collections found:', collections.length);
        
        // Test ping
        await db.admin().ping();
        console.log('✅ Ping successful');
        
    } catch (error) {
        console.error('❌ Connection failed:', error.message);
        console.error('Error code:', error.code);
        console.error('Error name:', error.name);
        
        // Additional debugging
        if (error.cause) {
            console.error('Cause:', error.cause.message);
        }
        
        process.exit(1);
    } finally {
        if (client) {
            await client.close();
            console.log('📝 Connection closed');
        }
    }
}

// Also test with different connection options
async function testWithDifferentOptions() {
    console.log('\n2. Testing with different connection options...');
    
    const options = [
        { name: 'Default timeout', opts: {} },
        { name: 'Long timeout', opts: { connectTimeoutMS: 30000, serverSelectionTimeoutMS: 30000 } },
        { name: 'IPv4 only', opts: { family: 4 } },
        { name: 'Direct connection', opts: { directConnection: true } }
    ];
    
    for (const option of options) {
        console.log(`\nTesting: ${option.name}`);
        try {
            const client = await MongoClient.connect(`mongodb://${config.host}:${config.port}/${config.database}`, option.opts);
            console.log(`✅ ${option.name} - Success`);
            await client.close();
        } catch (error) {
            console.log(`❌ ${option.name} - Failed: ${error.message}`);
        }
    }
}

async function main() {
    await testConnection();
    await testWithDifferentOptions();
}

main().catch(console.error);
