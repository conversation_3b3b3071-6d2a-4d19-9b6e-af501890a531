# Haraka Email Server Dashboard

A modern, responsive web dashboard for monitoring and managing your Haraka email server. Built with React, Node.js, Express, and MongoDB.

## Features

- **Real-time Dashboard**: Overview of email server performance and statistics
- **Transaction Management**: View, search, and filter email transactions
- **Detailed Analytics**: Comprehensive statistics and reporting
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional interface built with Tailwind CSS
- **RESTful API**: Backend API for integration with other tools

## Screenshots

### Dashboard Overview
- Server status and key metrics
- Recent transaction summary
- Top senders and recipients
- Status breakdown charts

### Transaction List
- Paginated transaction table
- Advanced search and filtering
- Sortable columns
- Export functionality

### Transaction Details
- Complete transaction information
- Email headers and metadata
- Connection and authentication details
- Timeline view

### Statistics
- Configurable time periods
- Status breakdowns
- Top senders/recipients analysis
- Visual charts and graphs

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Node.js API    │    │    MongoDB      │
│   (Port 3000)   │◄──►│  (Port 3001)    │◄──►│  (Port 27017)   │
│                 │    │                 │    │                 │
│ • Dashboard     │    │ • REST API      │    │ • Email Logs    │
│ • Transactions  │    │ • Authentication│    │ • Transactions  │
│ • Statistics    │    │ • Data Processing│    │ • Indexes       │
│ • Responsive UI │    │ • Rate Limiting │    │ • Aggregations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- MongoDB 5.0+
- Docker and Docker Compose (optional)

### Option 1: Docker Compose (Recommended)

1. **Clone and setup**:
   ```bash
   cd /var/www/tmv.co.il/test/haraka-dashboard
   ```

2. **Start services**:
   ```bash
   docker-compose up -d
   ```

3. **Access the dashboard**:
   - Frontend: http://localhost:3000
   - API: http://localhost:3001/api/health

### Option 2: Manual Installation

1. **Install backend dependencies**:
   ```bash
   cd backend
   npm install
   ```

2. **Install frontend dependencies**:
   ```bash
   cd ../frontend
   npm install
   ```

3. **Configure environment**:
   ```bash
   cd ../backend
   cp .env.example .env
   # Edit .env with your MongoDB connection details
   ```

4. **Start backend**:
   ```bash
   npm start
   ```

5. **Start frontend** (in new terminal):
   ```bash
   cd ../frontend
   npm start
   ```

## Configuration

### Backend Environment Variables

```env
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/haraka_email_logs
MONGODB_DB=haraka_email_logs

# Server Configuration
PORT=3001
NODE_ENV=development

# Security
API_KEY=your-secure-api-key-here

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
```

### Frontend Configuration

The frontend automatically connects to the backend API. For production deployments, update the API URL in the environment or nginx configuration.

## API Endpoints

### Health Check
- `GET /api/health` - Server health status

### Transactions
- `GET /api/transactions` - List transactions with pagination and filtering
- `GET /api/transactions/:id` - Get single transaction details

### Statistics
- `GET /api/stats` - Get server statistics and analytics

### Query Parameters

**Transactions endpoint supports**:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50)
- `sortBy` - Sort field (default: timestamp)
- `sortOrder` - Sort direction: asc/desc (default: desc)
- `search` - Search in sender, recipient, subject
- `status` - Filter by status
- `dateFrom` - Start date filter
- `dateTo` - End date filter

**Statistics endpoint supports**:
- `timeframe` - Time period: 1h, 24h, 7d, 30d (default: 24h)

## Development

### Backend Development

```bash
cd backend
npm run dev  # Uses nodemon for auto-restart
```

### Frontend Development

```bash
cd frontend
npm start    # React development server with hot reload
```

### Building for Production

```bash
# Backend
cd backend
npm start

# Frontend
cd frontend
npm run build
```

## Deployment

### Production with Docker

1. **Update environment variables** in `docker-compose.yml`
2. **Build and start**:
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

### Manual Production Deployment

1. **Build frontend**:
   ```bash
   cd frontend
   npm run build
   ```

2. **Serve with nginx** or copy build files to `/var/www/tmv.co.il/test`

3. **Start backend** with PM2 or similar process manager:
   ```bash
   cd backend
   npm install -g pm2
   pm2 start server.js --name haraka-dashboard-api
   ```

## Integration with Existing Haraka Setup

The dashboard connects to your existing MongoDB database where Haraka logs email transactions. Ensure:

1. **MongoDB is accessible** from the dashboard server
2. **Database name matches** the configuration (default: `haraka_email_logs`)
3. **Collection names match** the Haraka plugin configuration:
   - `email_transactions` - Main transaction logs
   - `email_connections` - Connection logs
   - `email_errors` - Error logs
   - `email_stats` - Statistics

## Security Considerations

- **Rate limiting** is enabled on the API
- **CORS** is configured for specific origins
- **Input validation** on all API endpoints
- **MongoDB injection protection**
- **Security headers** in nginx configuration

## Troubleshooting

### Common Issues

1. **Cannot connect to MongoDB**:
   - Check MongoDB is running: `systemctl status mongod`
   - Verify connection string in `.env`
   - Check firewall settings

2. **API returns 404**:
   - Ensure backend is running on port 3001
   - Check nginx proxy configuration
   - Verify API base URL in frontend

3. **No transactions showing**:
   - Verify Haraka is logging to MongoDB
   - Check database and collection names
   - Ensure MongoDB plugin is enabled in Haraka

4. **Frontend build fails**:
   - Clear node_modules and reinstall
   - Check Node.js version (18+ required)
   - Verify all dependencies are installed

### Logs

- **Backend logs**: `docker logs haraka-dashboard-api`
- **Frontend logs**: `docker logs haraka-dashboard-web`
- **MongoDB logs**: `docker logs haraka-mongodb`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Ensure all prerequisites are met
4. Verify configuration settings
