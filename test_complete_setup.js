#!/usr/bin/env node

// Complete Integration Test for Haraka Email Server Setup
// Tests all components: Dynamic IP, Header Stripping, and MongoDB Logging

const fs = require('fs');
const dns = require('dns');

console.log('🔍 Complete Haraka Setup Integration Test');
console.log('==========================================\n');

async function runCompleteTest() {
    let allTestsPassed = true;
    
    // Test 1: Dynamic IP Updater
    console.log('1. 🌐 Testing Dynamic IP Updater...');
    try {
        const dynamic_config = fs.readFileSync('./config/dynamic_ip_updater.ini', 'utf8');
        const dynamic_plugin = require('./plugins/dynamic_ip_updater.js');
        
        console.log('   ✅ Dynamic IP updater plugin loaded');
        console.log('   ✅ Configuration file exists');
        
        // Test DNS resolution
        const hostname = 'hallocktest6.tmv.co.il';
        await new Promise((resolve, reject) => {
            dns.resolve4(hostname, (err, addresses) => {
                if (err) {
                    console.log('   ❌ DNS resolution failed:', err.message);
                    allTestsPassed = false;
                    reject(err);
                } else {
                    console.log(`   ✅ DNS resolved: ${hostname} → ${addresses[0]}`);
                    resolve(addresses);
                }
            });
        });
        
    } catch (error) {
        console.log('   ❌ Dynamic IP updater test failed:', error.message);
        allTestsPassed = false;
    }
    
    // Test 2: Header Stripping
    console.log('\n2. 🛡️ Testing Header Stripping...');
    try {
        const strip_config = fs.readFileSync('./config/strip_relay_headers.ini', 'utf8');
        const aggressive_config = fs.readFileSync('./config/aggressive_ip_stripper.ini', 'utf8');
        const strip_plugin = require('./plugins/strip_relay_headers.js');
        const aggressive_plugin = require('./plugins/aggressive_ip_stripper.js');
        
        console.log('   ✅ Header stripping plugins loaded');
        console.log('   ✅ Configuration files exist');
        console.log('   ✅ Privacy protection configured');
        
    } catch (error) {
        console.log('   ❌ Header stripping test failed:', error.message);
        allTestsPassed = false;
    }
    
    // Test 3: MongoDB Logging
    console.log('\n3. 📊 Testing MongoDB Logging...');
    try {
        const mongodb_config = fs.readFileSync('./config/mongodb_logger.ini', 'utf8');
        
        // Check if MongoDB module is available
        try {
            require('mongodb');
            console.log('   ✅ MongoDB module available');
        } catch (err) {
            console.log('   ⚠️  MongoDB module not installed (run: npm install mongodb)');
        }
        
        const mongodb_plugin = require('./plugins/mongodb_logger.js');
        console.log('   ✅ MongoDB logging plugin loaded');
        console.log('   ✅ Configuration file exists');
        
        // Test MongoDB connection (if available)
        try {
            const { MongoClient } = require('mongodb');
            const client = await MongoClient.connect('mongodb://localhost:27017/haraka_email_logs', {
                connectTimeoutMS: 2000,
                serverSelectionTimeoutMS: 2000
            });
            await client.close();
            console.log('   ✅ MongoDB connection successful');
        } catch (err) {
            console.log('   ⚠️  MongoDB connection failed (ensure MongoDB is running)');
        }
        
    } catch (error) {
        console.log('   ❌ MongoDB logging test failed:', error.message);
        allTestsPassed = false;
    }
    
    // Test 4: Plugin Configuration
    console.log('\n4. ⚙️ Testing Plugin Configuration...');
    try {
        const plugins_content = fs.readFileSync('./config/plugins', 'utf8');
        
        const required_plugins = [
            'dynamic_ip_updater',
            'strip_relay_headers', 
            'aggressive_ip_stripper',
            'mongodb_logger'
        ];
        
        const disabled_plugins = [
            '# spf'  // SPF should be disabled
        ];
        
        required_plugins.forEach(plugin => {
            if (plugins_content.includes(plugin)) {
                console.log(`   ✅ ${plugin} enabled`);
            } else {
                console.log(`   ❌ ${plugin} NOT enabled`);
                allTestsPassed = false;
            }
        });
        
        disabled_plugins.forEach(plugin => {
            if (plugins_content.includes(plugin)) {
                console.log(`   ✅ SPF plugin disabled`);
            } else {
                console.log(`   ⚠️  SPF plugin may still be enabled`);
            }
        });
        
    } catch (error) {
        console.log('   ❌ Plugin configuration test failed:', error.message);
        allTestsPassed = false;
    }
    
    // Test 5: Integration Compatibility
    console.log('\n5. 🔗 Testing Integration Compatibility...');
    try {
        // Check that privacy settings are consistent
        const strip_config = fs.readFileSync('./config/strip_relay_headers.ini', 'utf8');
        const aggressive_config = fs.readFileSync('./config/aggressive_ip_stripper.ini', 'utf8');
        const mongodb_config = fs.readFileSync('./config/mongodb_logger.ini', 'utf8');
        
        // Check hostname consistency
        const hostname_pattern = /hostname\s*=\s*(.+)/;
        const strip_hostname = strip_config.match(hostname_pattern)?.[1]?.trim();
        const aggressive_hostname = aggressive_config.match(hostname_pattern)?.[1]?.trim();
        
        if (strip_hostname === aggressive_hostname) {
            console.log(`   ✅ Hostname consistent across plugins: ${strip_hostname}`);
        } else {
            console.log(`   ⚠️  Hostname mismatch between plugins`);
        }
        
        // Check privacy settings
        if (mongodb_config.includes('sanitize_headers=true')) {
            console.log('   ✅ MongoDB header sanitization enabled');
        } else {
            console.log('   ⚠️  MongoDB header sanitization not enabled');
        }
        
        if (mongodb_config.includes('strip_private_ips=true')) {
            console.log('   ✅ MongoDB IP stripping enabled');
        } else {
            console.log('   ⚠️  MongoDB IP stripping not enabled');
        }
        
        console.log('   ✅ Integration compatibility verified');
        
    } catch (error) {
        console.log('   ❌ Integration test failed:', error.message);
        allTestsPassed = false;
    }
    
    // Test 6: File Structure
    console.log('\n6. 📁 Testing File Structure...');
    const required_files = [
        'plugins/dynamic_ip_updater.js',
        'plugins/strip_relay_headers.js',
        'plugins/aggressive_ip_stripper.js',
        'plugins/mongodb_logger.js',
        'config/dynamic_ip_updater.ini',
        'config/strip_relay_headers.ini',
        'config/aggressive_ip_stripper.ini',
        'config/mongodb_logger.ini',
        'mongodb_schema.js',
        'test_mongodb_logging.js',
        'mongodb_queries.js',
        'MONGODB_LOGGING_SETUP.md'
    ];
    
    required_files.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`   ✅ ${file}`);
        } else {
            console.log(`   ❌ ${file} missing`);
            allTestsPassed = false;
        }
    });
    
    // Final Results
    console.log('\n' + '='.repeat(50));
    if (allTestsPassed) {
        console.log('🎉 ALL TESTS PASSED! Your Haraka setup is ready!');
        console.log('\nNext steps:');
        console.log('1. Install MongoDB: sudo apt install mongodb');
        console.log('2. Install MongoDB driver: npm install mongodb');
        console.log('3. Setup database: node mongodb_schema.js');
        console.log('4. Restart Haraka to load all plugins');
        console.log('5. Send test emails and monitor logs');
        console.log('6. Use mongodb_queries.js to analyze data');
    } else {
        console.log('⚠️  SOME TESTS FAILED - Review the issues above');
        console.log('\nCommon fixes:');
        console.log('- Ensure all configuration files are present');
        console.log('- Check plugin syntax with: node -c plugins/plugin_name.js');
        console.log('- Verify plugins are enabled in config/plugins');
        console.log('- Install missing dependencies');
    }
    
    console.log('\n📊 Setup Summary:');
    console.log('- 🌐 Dynamic IP management for hallocktest6.tmv.co.il');
    console.log('- 🛡️ Complete IP privacy protection');
    console.log('- 📊 Comprehensive MongoDB email logging');
    console.log('- 🔗 Integrated privacy-aware audit trail');
    console.log('- 📈 Dashboard-ready data structure');
    
    return allTestsPassed;
}

// Run the complete test
runCompleteTest()
    .then(success => {
        process.exit(success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ Test execution failed:', error.message);
        process.exit(1);
    });
